from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_tickers = publicGetTickers = Entry('tickers', 'public', 'GET', {'cost': 1})
    public_get_tickers_symbol = publicGetTickersSymbol = Entry('tickers/{symbol}', 'public', 'GET', {'cost': 1})
    public_get_symbols = publicGetSymbols = Entry('symbols', 'public', 'GET', {'cost': 1})
    public_get_symbols_symbol = publicGetSymbolsSymbol = Entry('symbols/{symbol}', 'public', 'GET', {'cost': 1})
    public_get_l2_symbol = publicGetL2Symbol = Entry('l2/{symbol}', 'public', 'GET', {'cost': 1})
    public_get_l3_symbol = publicGetL3Symbol = Entry('l3/{symbol}', 'public', 'GET', {'cost': 1})
    private_get_fees = privateGetFees = Entry('fees', 'private', 'GET', {'cost': 1})
    private_get_orders = privateGetOrders = Entry('orders', 'private', 'GET', {'cost': 1})
    private_get_orders_orderid = privateGetOrdersOrderId = Entry('orders/{orderId}', 'private', 'GET', {'cost': 1})
    private_get_trades = privateGetTrades = Entry('trades', 'private', 'GET', {'cost': 1})
    private_get_fills = privateGetFills = Entry('fills', 'private', 'GET', {'cost': 1})
    private_get_deposits = privateGetDeposits = Entry('deposits', 'private', 'GET', {'cost': 1})
    private_get_deposits_depositid = privateGetDepositsDepositId = Entry('deposits/{depositId}', 'private', 'GET', {'cost': 1})
    private_get_accounts = privateGetAccounts = Entry('accounts', 'private', 'GET', {'cost': 1})
    private_get_accounts_account_currency = privateGetAccountsAccountCurrency = Entry('accounts/{account}/{currency}', 'private', 'GET', {'cost': 1})
    private_get_whitelist = privateGetWhitelist = Entry('whitelist', 'private', 'GET', {'cost': 1})
    private_get_whitelist_currency = privateGetWhitelistCurrency = Entry('whitelist/{currency}', 'private', 'GET', {'cost': 1})
    private_get_withdrawals = privateGetWithdrawals = Entry('withdrawals', 'private', 'GET', {'cost': 1})
    private_get_withdrawals_withdrawalid = privateGetWithdrawalsWithdrawalId = Entry('withdrawals/{withdrawalId}', 'private', 'GET', {'cost': 1})
    private_post_orders = privatePostOrders = Entry('orders', 'private', 'POST', {'cost': 1})
    private_post_deposits_currency = privatePostDepositsCurrency = Entry('deposits/{currency}', 'private', 'POST', {'cost': 1})
    private_post_withdrawals = privatePostWithdrawals = Entry('withdrawals', 'private', 'POST', {'cost': 1})
    private_delete_orders = privateDeleteOrders = Entry('orders', 'private', 'DELETE', {'cost': 1})
    private_delete_orders_orderid = privateDeleteOrdersOrderId = Entry('orders/{orderId}', 'private', 'DELETE', {'cost': 1})
