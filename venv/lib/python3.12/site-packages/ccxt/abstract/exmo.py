from ccxt.base.types import Entry


class ImplicitAPI:
    web_get_ctrl_feesandlimits = webGetCtrlFeesAndLimits = Entry('ctrl/feesAndLimits', 'web', 'GET', {})
    web_get_en_docs_fees = webGetEnDocsFees = Entry('en/docs/fees', 'web', 'GET', {})
    public_get_currency = publicGetCurrency = Entry('currency', 'public', 'GET', {})
    public_get_currency_list_extended = publicGetCurrencyListExtended = Entry('currency/list/extended', 'public', 'GET', {})
    public_get_order_book = publicGetOrderBook = Entry('order_book', 'public', 'GET', {})
    public_get_pair_settings = publicGetPairSettings = Entry('pair_settings', 'public', 'GET', {})
    public_get_ticker = publicGetTicker = Entry('ticker', 'public', 'GET', {})
    public_get_trades = publicGetTrades = Entry('trades', 'public', 'GET', {})
    public_get_candles_history = publicGetCandlesHistory = Entry('candles_history', 'public', 'GET', {})
    public_get_required_amount = publicGetRequiredAmount = Entry('required_amount', 'public', 'GET', {})
    public_get_payments_providers_crypto_list = publicGetPaymentsProvidersCryptoList = Entry('payments/providers/crypto/list', 'public', 'GET', {})
    private_post_user_info = privatePostUserInfo = Entry('user_info', 'private', 'POST', {})
    private_post_order_create = privatePostOrderCreate = Entry('order_create', 'private', 'POST', {})
    private_post_order_cancel = privatePostOrderCancel = Entry('order_cancel', 'private', 'POST', {})
    private_post_stop_market_order_create = privatePostStopMarketOrderCreate = Entry('stop_market_order_create', 'private', 'POST', {})
    private_post_stop_market_order_cancel = privatePostStopMarketOrderCancel = Entry('stop_market_order_cancel', 'private', 'POST', {})
    private_post_user_open_orders = privatePostUserOpenOrders = Entry('user_open_orders', 'private', 'POST', {})
    private_post_user_trades = privatePostUserTrades = Entry('user_trades', 'private', 'POST', {})
    private_post_user_cancelled_orders = privatePostUserCancelledOrders = Entry('user_cancelled_orders', 'private', 'POST', {})
    private_post_order_trades = privatePostOrderTrades = Entry('order_trades', 'private', 'POST', {})
    private_post_deposit_address = privatePostDepositAddress = Entry('deposit_address', 'private', 'POST', {})
    private_post_withdraw_crypt = privatePostWithdrawCrypt = Entry('withdraw_crypt', 'private', 'POST', {})
    private_post_withdraw_get_txid = privatePostWithdrawGetTxid = Entry('withdraw_get_txid', 'private', 'POST', {})
    private_post_excode_create = privatePostExcodeCreate = Entry('excode_create', 'private', 'POST', {})
    private_post_excode_load = privatePostExcodeLoad = Entry('excode_load', 'private', 'POST', {})
    private_post_code_check = privatePostCodeCheck = Entry('code_check', 'private', 'POST', {})
    private_post_wallet_history = privatePostWalletHistory = Entry('wallet_history', 'private', 'POST', {})
    private_post_wallet_operations = privatePostWalletOperations = Entry('wallet_operations', 'private', 'POST', {})
    private_post_margin_user_order_create = privatePostMarginUserOrderCreate = Entry('margin/user/order/create', 'private', 'POST', {})
    private_post_margin_user_order_update = privatePostMarginUserOrderUpdate = Entry('margin/user/order/update', 'private', 'POST', {})
    private_post_margin_user_order_cancel = privatePostMarginUserOrderCancel = Entry('margin/user/order/cancel', 'private', 'POST', {})
    private_post_margin_user_position_close = privatePostMarginUserPositionClose = Entry('margin/user/position/close', 'private', 'POST', {})
    private_post_margin_user_position_margin_add = privatePostMarginUserPositionMarginAdd = Entry('margin/user/position/margin_add', 'private', 'POST', {})
    private_post_margin_user_position_margin_remove = privatePostMarginUserPositionMarginRemove = Entry('margin/user/position/margin_remove', 'private', 'POST', {})
    private_post_margin_currency_list = privatePostMarginCurrencyList = Entry('margin/currency/list', 'private', 'POST', {})
    private_post_margin_pair_list = privatePostMarginPairList = Entry('margin/pair/list', 'private', 'POST', {})
    private_post_margin_settings = privatePostMarginSettings = Entry('margin/settings', 'private', 'POST', {})
    private_post_margin_funding_list = privatePostMarginFundingList = Entry('margin/funding/list', 'private', 'POST', {})
    private_post_margin_user_info = privatePostMarginUserInfo = Entry('margin/user/info', 'private', 'POST', {})
    private_post_margin_user_order_list = privatePostMarginUserOrderList = Entry('margin/user/order/list', 'private', 'POST', {})
    private_post_margin_user_order_history = privatePostMarginUserOrderHistory = Entry('margin/user/order/history', 'private', 'POST', {})
    private_post_margin_user_order_trades = privatePostMarginUserOrderTrades = Entry('margin/user/order/trades', 'private', 'POST', {})
    private_post_margin_user_order_max_quantity = privatePostMarginUserOrderMaxQuantity = Entry('margin/user/order/max_quantity', 'private', 'POST', {})
    private_post_margin_user_position_list = privatePostMarginUserPositionList = Entry('margin/user/position/list', 'private', 'POST', {})
    private_post_margin_user_position_margin_remove_info = privatePostMarginUserPositionMarginRemoveInfo = Entry('margin/user/position/margin_remove_info', 'private', 'POST', {})
    private_post_margin_user_position_margin_add_info = privatePostMarginUserPositionMarginAddInfo = Entry('margin/user/position/margin_add_info', 'private', 'POST', {})
    private_post_margin_user_wallet_list = privatePostMarginUserWalletList = Entry('margin/user/wallet/list', 'private', 'POST', {})
    private_post_margin_user_wallet_history = privatePostMarginUserWalletHistory = Entry('margin/user/wallet/history', 'private', 'POST', {})
    private_post_margin_user_trade_list = privatePostMarginUserTradeList = Entry('margin/user/trade/list', 'private', 'POST', {})
    private_post_margin_trades = privatePostMarginTrades = Entry('margin/trades', 'private', 'POST', {})
    private_post_margin_liquidation_feed = privatePostMarginLiquidationFeed = Entry('margin/liquidation/feed', 'private', 'POST', {})
