from ccxt.base.types import Entry


class ImplicitAPI:
    public_common_get_v2_public_annoucements = publicCommonGetV2PublicAnnoucements = Entry('v2/public/annoucements', ['public', 'common'], 'GET', {'cost': 1})
    public_common_get_v2_public_time = publicCommonGetV2PublicTime = Entry('v2/public/time', ['public', 'common'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_notice_queryallnotices = publicSpotGetSpotV1NoticeQueryAllNotices = Entry('spot/v1/notice/queryAllNotices', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_public_time = publicSpotGetSpotV1PublicTime = Entry('spot/v1/public/time', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_public_currencies = publicSpotGetSpotV1PublicCurrencies = Entry('spot/v1/public/currencies', ['public', 'spot'], 'GET', {'cost': 6.6667})
    public_spot_get_spot_v1_public_products = publicSpotGetSpotV1PublicProducts = Entry('spot/v1/public/products', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_public_product = publicSpotGetSpotV1PublicProduct = Entry('spot/v1/public/product', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_ticker = publicSpotGetSpotV1MarketTicker = Entry('spot/v1/market/ticker', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_tickers = publicSpotGetSpotV1MarketTickers = Entry('spot/v1/market/tickers', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_fills = publicSpotGetSpotV1MarketFills = Entry('spot/v1/market/fills', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_spot_v1_market_fills_history = publicSpotGetSpotV1MarketFillsHistory = Entry('spot/v1/market/fills-history', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_spot_v1_market_candles = publicSpotGetSpotV1MarketCandles = Entry('spot/v1/market/candles', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_depth = publicSpotGetSpotV1MarketDepth = Entry('spot/v1/market/depth', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_spot_vip_level = publicSpotGetSpotV1MarketSpotVipLevel = Entry('spot/v1/market/spot-vip-level', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_spot_v1_market_merge_depth = publicSpotGetSpotV1MarketMergeDepth = Entry('spot/v1/market/merge-depth', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_market_history_candles = publicSpotGetSpotV1MarketHistoryCandles = Entry('spot/v1/market/history-candles', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_spot_v1_public_loan_coininfos = publicSpotGetSpotV1PublicLoanCoinInfos = Entry('spot/v1/public/loan/coinInfos', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_spot_v1_public_loan_hour_interest = publicSpotGetSpotV1PublicLoanHourInterest = Entry('spot/v1/public/loan/hour-interest', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_v2_spot_public_coins = publicSpotGetV2SpotPublicCoins = Entry('v2/spot/public/coins', ['public', 'spot'], 'GET', {'cost': 6.6667})
    public_spot_get_v2_spot_public_symbols = publicSpotGetV2SpotPublicSymbols = Entry('v2/spot/public/symbols', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_vip_fee_rate = publicSpotGetV2SpotMarketVipFeeRate = Entry('v2/spot/market/vip-fee-rate', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_v2_spot_market_tickers = publicSpotGetV2SpotMarketTickers = Entry('v2/spot/market/tickers', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_merge_depth = publicSpotGetV2SpotMarketMergeDepth = Entry('v2/spot/market/merge-depth', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_orderbook = publicSpotGetV2SpotMarketOrderbook = Entry('v2/spot/market/orderbook', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_candles = publicSpotGetV2SpotMarketCandles = Entry('v2/spot/market/candles', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_history_candles = publicSpotGetV2SpotMarketHistoryCandles = Entry('v2/spot/market/history-candles', ['public', 'spot'], 'GET', {'cost': 1})
    public_spot_get_v2_spot_market_fills = publicSpotGetV2SpotMarketFills = Entry('v2/spot/market/fills', ['public', 'spot'], 'GET', {'cost': 2})
    public_spot_get_v2_spot_market_fills_history = publicSpotGetV2SpotMarketFillsHistory = Entry('v2/spot/market/fills-history', ['public', 'spot'], 'GET', {'cost': 2})
    public_mix_get_mix_v1_market_contracts = publicMixGetMixV1MarketContracts = Entry('mix/v1/market/contracts', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_depth = publicMixGetMixV1MarketDepth = Entry('mix/v1/market/depth', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_ticker = publicMixGetMixV1MarketTicker = Entry('mix/v1/market/ticker', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_tickers = publicMixGetMixV1MarketTickers = Entry('mix/v1/market/tickers', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_contract_vip_level = publicMixGetMixV1MarketContractVipLevel = Entry('mix/v1/market/contract-vip-level', ['public', 'mix'], 'GET', {'cost': 2})
    public_mix_get_mix_v1_market_fills = publicMixGetMixV1MarketFills = Entry('mix/v1/market/fills', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_fills_history = publicMixGetMixV1MarketFillsHistory = Entry('mix/v1/market/fills-history', ['public', 'mix'], 'GET', {'cost': 2})
    public_mix_get_mix_v1_market_candles = publicMixGetMixV1MarketCandles = Entry('mix/v1/market/candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_index = publicMixGetMixV1MarketIndex = Entry('mix/v1/market/index', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_funding_time = publicMixGetMixV1MarketFundingTime = Entry('mix/v1/market/funding-time', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_history_fundrate = publicMixGetMixV1MarketHistoryFundRate = Entry('mix/v1/market/history-fundRate', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_current_fundrate = publicMixGetMixV1MarketCurrentFundRate = Entry('mix/v1/market/current-fundRate', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_open_interest = publicMixGetMixV1MarketOpenInterest = Entry('mix/v1/market/open-interest', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_mark_price = publicMixGetMixV1MarketMarkPrice = Entry('mix/v1/market/mark-price', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_symbol_leverage = publicMixGetMixV1MarketSymbolLeverage = Entry('mix/v1/market/symbol-leverage', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_querypositionlever = publicMixGetMixV1MarketQueryPositionLever = Entry('mix/v1/market/queryPositionLever', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_open_limit = publicMixGetMixV1MarketOpenLimit = Entry('mix/v1/market/open-limit', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_history_candles = publicMixGetMixV1MarketHistoryCandles = Entry('mix/v1/market/history-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_history_index_candles = publicMixGetMixV1MarketHistoryIndexCandles = Entry('mix/v1/market/history-index-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_history_mark_candles = publicMixGetMixV1MarketHistoryMarkCandles = Entry('mix/v1/market/history-mark-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_mix_v1_market_merge_depth = publicMixGetMixV1MarketMergeDepth = Entry('mix/v1/market/merge-depth', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_vip_fee_rate = publicMixGetV2MixMarketVipFeeRate = Entry('v2/mix/market/vip-fee-rate', ['public', 'mix'], 'GET', {'cost': 2})
    public_mix_get_v2_mix_market_merge_depth = publicMixGetV2MixMarketMergeDepth = Entry('v2/mix/market/merge-depth', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_ticker = publicMixGetV2MixMarketTicker = Entry('v2/mix/market/ticker', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_tickers = publicMixGetV2MixMarketTickers = Entry('v2/mix/market/tickers', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_fills = publicMixGetV2MixMarketFills = Entry('v2/mix/market/fills', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_fills_history = publicMixGetV2MixMarketFillsHistory = Entry('v2/mix/market/fills-history', ['public', 'mix'], 'GET', {'cost': 2})
    public_mix_get_v2_mix_market_candles = publicMixGetV2MixMarketCandles = Entry('v2/mix/market/candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_history_candles = publicMixGetV2MixMarketHistoryCandles = Entry('v2/mix/market/history-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_history_index_candles = publicMixGetV2MixMarketHistoryIndexCandles = Entry('v2/mix/market/history-index-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_history_mark_candles = publicMixGetV2MixMarketHistoryMarkCandles = Entry('v2/mix/market/history-mark-candles', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_open_interest = publicMixGetV2MixMarketOpenInterest = Entry('v2/mix/market/open-interest', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_funding_time = publicMixGetV2MixMarketFundingTime = Entry('v2/mix/market/funding-time', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_symbol_price = publicMixGetV2MixMarketSymbolPrice = Entry('v2/mix/market/symbol-price', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_history_fund_rate = publicMixGetV2MixMarketHistoryFundRate = Entry('v2/mix/market/history-fund-rate', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_current_fund_rate = publicMixGetV2MixMarketCurrentFundRate = Entry('v2/mix/market/current-fund-rate', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_contracts = publicMixGetV2MixMarketContracts = Entry('v2/mix/market/contracts', ['public', 'mix'], 'GET', {'cost': 1})
    public_mix_get_v2_mix_market_query_position_lever = publicMixGetV2MixMarketQueryPositionLever = Entry('v2/mix/market/query-position-lever', ['public', 'mix'], 'GET', {'cost': 2})
    public_mix_get_v2_mix_market_account_long_short = publicMixGetV2MixMarketAccountLongShort = Entry('v2/mix/market/account-long-short', ['public', 'mix'], 'GET', {'cost': 20})
    public_margin_get_margin_v1_cross_public_interestrateandlimit = publicMarginGetMarginV1CrossPublicInterestRateAndLimit = Entry('margin/v1/cross/public/interestRateAndLimit', ['public', 'margin'], 'GET', {'cost': 2})
    public_margin_get_margin_v1_isolated_public_interestrateandlimit = publicMarginGetMarginV1IsolatedPublicInterestRateAndLimit = Entry('margin/v1/isolated/public/interestRateAndLimit', ['public', 'margin'], 'GET', {'cost': 2})
    public_margin_get_margin_v1_cross_public_tierdata = publicMarginGetMarginV1CrossPublicTierData = Entry('margin/v1/cross/public/tierData', ['public', 'margin'], 'GET', {'cost': 2})
    public_margin_get_margin_v1_isolated_public_tierdata = publicMarginGetMarginV1IsolatedPublicTierData = Entry('margin/v1/isolated/public/tierData', ['public', 'margin'], 'GET', {'cost': 2})
    public_margin_get_margin_v1_public_currencies = publicMarginGetMarginV1PublicCurrencies = Entry('margin/v1/public/currencies', ['public', 'margin'], 'GET', {'cost': 1})
    public_margin_get_v2_margin_currencies = publicMarginGetV2MarginCurrencies = Entry('v2/margin/currencies', ['public', 'margin'], 'GET', {'cost': 2})
    public_margin_get_v2_margin_market_long_short_ratio = publicMarginGetV2MarginMarketLongShortRatio = Entry('v2/margin/market/long-short-ratio', ['public', 'margin'], 'GET', {'cost': 20})
    public_earn_get_v2_earn_loan_public_coininfos = publicEarnGetV2EarnLoanPublicCoinInfos = Entry('v2/earn/loan/public/coinInfos', ['public', 'earn'], 'GET', {'cost': 2})
    public_earn_get_v2_earn_loan_public_hour_interest = publicEarnGetV2EarnLoanPublicHourInterest = Entry('v2/earn/loan/public/hour-interest', ['public', 'earn'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_wallet_deposit_address = privateSpotGetSpotV1WalletDepositAddress = Entry('spot/v1/wallet/deposit-address', ['private', 'spot'], 'GET', {'cost': 4})
    private_spot_get_spot_v1_wallet_withdrawal_list = privateSpotGetSpotV1WalletWithdrawalList = Entry('spot/v1/wallet/withdrawal-list', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_spot_v1_wallet_deposit_list = privateSpotGetSpotV1WalletDepositList = Entry('spot/v1/wallet/deposit-list', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_spot_v1_account_getinfo = privateSpotGetSpotV1AccountGetInfo = Entry('spot/v1/account/getInfo', ['private', 'spot'], 'GET', {'cost': 20})
    private_spot_get_spot_v1_account_assets = privateSpotGetSpotV1AccountAssets = Entry('spot/v1/account/assets', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_account_assets_lite = privateSpotGetSpotV1AccountAssetsLite = Entry('spot/v1/account/assets-lite', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_account_transferrecords = privateSpotGetSpotV1AccountTransferRecords = Entry('spot/v1/account/transferRecords', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_spot_v1_convert_currencies = privateSpotGetSpotV1ConvertCurrencies = Entry('spot/v1/convert/currencies', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_convert_convert_record = privateSpotGetSpotV1ConvertConvertRecord = Entry('spot/v1/convert/convert-record', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_loan_ongoing_orders = privateSpotGetSpotV1LoanOngoingOrders = Entry('spot/v1/loan/ongoing-orders', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_loan_repay_history = privateSpotGetSpotV1LoanRepayHistory = Entry('spot/v1/loan/repay-history', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_loan_revise_history = privateSpotGetSpotV1LoanReviseHistory = Entry('spot/v1/loan/revise-history', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_loan_borrow_history = privateSpotGetSpotV1LoanBorrowHistory = Entry('spot/v1/loan/borrow-history', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_spot_v1_loan_debts = privateSpotGetSpotV1LoanDebts = Entry('spot/v1/loan/debts', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_trade_orderinfo = privateSpotGetV2SpotTradeOrderInfo = Entry('v2/spot/trade/orderInfo', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_spot_trade_unfilled_orders = privateSpotGetV2SpotTradeUnfilledOrders = Entry('v2/spot/trade/unfilled-orders', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_spot_trade_history_orders = privateSpotGetV2SpotTradeHistoryOrders = Entry('v2/spot/trade/history-orders', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_spot_trade_fills = privateSpotGetV2SpotTradeFills = Entry('v2/spot/trade/fills', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_trade_current_plan_order = privateSpotGetV2SpotTradeCurrentPlanOrder = Entry('v2/spot/trade/current-plan-order', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_spot_trade_history_plan_order = privateSpotGetV2SpotTradeHistoryPlanOrder = Entry('v2/spot/trade/history-plan-order', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_spot_account_info = privateSpotGetV2SpotAccountInfo = Entry('v2/spot/account/info', ['private', 'spot'], 'GET', {'cost': 20})
    private_spot_get_v2_spot_account_assets = privateSpotGetV2SpotAccountAssets = Entry('v2/spot/account/assets', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_account_subaccount_assets = privateSpotGetV2SpotAccountSubaccountAssets = Entry('v2/spot/account/subaccount-assets', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_account_bills = privateSpotGetV2SpotAccountBills = Entry('v2/spot/account/bills', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_account_transferrecords = privateSpotGetV2SpotAccountTransferRecords = Entry('v2/spot/account/transferRecords', ['private', 'spot'], 'GET', {'cost': 1})
    private_spot_get_v2_account_funding_assets = privateSpotGetV2AccountFundingAssets = Entry('v2/account/funding-assets', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_account_bot_assets = privateSpotGetV2AccountBotAssets = Entry('v2/account/bot-assets', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_account_all_account_balance = privateSpotGetV2AccountAllAccountBalance = Entry('v2/account/all-account-balance', ['private', 'spot'], 'GET', {'cost': 20})
    private_spot_get_v2_spot_wallet_deposit_address = privateSpotGetV2SpotWalletDepositAddress = Entry('v2/spot/wallet/deposit-address', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_wallet_deposit_records = privateSpotGetV2SpotWalletDepositRecords = Entry('v2/spot/wallet/deposit-records', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_get_v2_spot_wallet_withdrawal_records = privateSpotGetV2SpotWalletWithdrawalRecords = Entry('v2/spot/wallet/withdrawal-records', ['private', 'spot'], 'GET', {'cost': 2})
    private_spot_post_spot_v1_wallet_transfer = privateSpotPostSpotV1WalletTransfer = Entry('spot/v1/wallet/transfer', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_wallet_transfer_v2 = privateSpotPostSpotV1WalletTransferV2 = Entry('spot/v1/wallet/transfer-v2', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_wallet_subtransfer = privateSpotPostSpotV1WalletSubTransfer = Entry('spot/v1/wallet/subTransfer', ['private', 'spot'], 'POST', {'cost': 10})
    private_spot_post_spot_v1_wallet_withdrawal = privateSpotPostSpotV1WalletWithdrawal = Entry('spot/v1/wallet/withdrawal', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_wallet_withdrawal_v2 = privateSpotPostSpotV1WalletWithdrawalV2 = Entry('spot/v1/wallet/withdrawal-v2', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_wallet_withdrawal_inner = privateSpotPostSpotV1WalletWithdrawalInner = Entry('spot/v1/wallet/withdrawal-inner', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_wallet_withdrawal_inner_v2 = privateSpotPostSpotV1WalletWithdrawalInnerV2 = Entry('spot/v1/wallet/withdrawal-inner-v2', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_account_sub_account_spot_assets = privateSpotPostSpotV1AccountSubAccountSpotAssets = Entry('spot/v1/account/sub-account-spot-assets', ['private', 'spot'], 'POST', {'cost': 200})
    private_spot_post_spot_v1_account_bills = privateSpotPostSpotV1AccountBills = Entry('spot/v1/account/bills', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trade_orders = privateSpotPostSpotV1TradeOrders = Entry('spot/v1/trade/orders', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trade_batch_orders = privateSpotPostSpotV1TradeBatchOrders = Entry('spot/v1/trade/batch-orders', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_trade_cancel_order = privateSpotPostSpotV1TradeCancelOrder = Entry('spot/v1/trade/cancel-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trade_cancel_order_v2 = privateSpotPostSpotV1TradeCancelOrderV2 = Entry('spot/v1/trade/cancel-order-v2', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trade_cancel_symbol_order = privateSpotPostSpotV1TradeCancelSymbolOrder = Entry('spot/v1/trade/cancel-symbol-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trade_cancel_batch_orders = privateSpotPostSpotV1TradeCancelBatchOrders = Entry('spot/v1/trade/cancel-batch-orders', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_trade_cancel_batch_orders_v2 = privateSpotPostSpotV1TradeCancelBatchOrdersV2 = Entry('spot/v1/trade/cancel-batch-orders-v2', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_trade_orderinfo = privateSpotPostSpotV1TradeOrderInfo = Entry('spot/v1/trade/orderInfo', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_trade_open_orders = privateSpotPostSpotV1TradeOpenOrders = Entry('spot/v1/trade/open-orders', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_trade_history = privateSpotPostSpotV1TradeHistory = Entry('spot/v1/trade/history', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_trade_fills = privateSpotPostSpotV1TradeFills = Entry('spot/v1/trade/fills', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_placeplan = privateSpotPostSpotV1PlanPlacePlan = Entry('spot/v1/plan/placePlan', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_modifyplan = privateSpotPostSpotV1PlanModifyPlan = Entry('spot/v1/plan/modifyPlan', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_cancelplan = privateSpotPostSpotV1PlanCancelPlan = Entry('spot/v1/plan/cancelPlan', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_currentplan = privateSpotPostSpotV1PlanCurrentPlan = Entry('spot/v1/plan/currentPlan', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_historyplan = privateSpotPostSpotV1PlanHistoryPlan = Entry('spot/v1/plan/historyPlan', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_spot_v1_plan_batchcancelplan = privateSpotPostSpotV1PlanBatchCancelPlan = Entry('spot/v1/plan/batchCancelPlan', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_convert_quoted_price = privateSpotPostSpotV1ConvertQuotedPrice = Entry('spot/v1/convert/quoted-price', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_convert_trade = privateSpotPostSpotV1ConvertTrade = Entry('spot/v1/convert/trade', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_spot_v1_loan_borrow = privateSpotPostSpotV1LoanBorrow = Entry('spot/v1/loan/borrow', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_loan_repay = privateSpotPostSpotV1LoanRepay = Entry('spot/v1/loan/repay', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_loan_revise_pledge = privateSpotPostSpotV1LoanRevisePledge = Entry('spot/v1/loan/revise-pledge', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_ordercurrentlist = privateSpotPostSpotV1TraceOrderOrderCurrentList = Entry('spot/v1/trace/order/orderCurrentList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_orderhistorylist = privateSpotPostSpotV1TraceOrderOrderHistoryList = Entry('spot/v1/trace/order/orderHistoryList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_closetrackingorder = privateSpotPostSpotV1TraceOrderCloseTrackingOrder = Entry('spot/v1/trace/order/closeTrackingOrder', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_updatetpsl = privateSpotPostSpotV1TraceOrderUpdateTpsl = Entry('spot/v1/trace/order/updateTpsl', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_followerendorder = privateSpotPostSpotV1TraceOrderFollowerEndOrder = Entry('spot/v1/trace/order/followerEndOrder', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_order_spotinfolist = privateSpotPostSpotV1TraceOrderSpotInfoList = Entry('spot/v1/trace/order/spotInfoList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_config_gettradersettings = privateSpotPostSpotV1TraceConfigGetTraderSettings = Entry('spot/v1/trace/config/getTraderSettings', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_config_getfollowersettings = privateSpotPostSpotV1TraceConfigGetFollowerSettings = Entry('spot/v1/trace/config/getFollowerSettings', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_user_mytraders = privateSpotPostSpotV1TraceUserMyTraders = Entry('spot/v1/trace/user/myTraders', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_config_setfollowerconfig = privateSpotPostSpotV1TraceConfigSetFollowerConfig = Entry('spot/v1/trace/config/setFollowerConfig', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_user_myfollowers = privateSpotPostSpotV1TraceUserMyFollowers = Entry('spot/v1/trace/user/myFollowers', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_config_setproductcode = privateSpotPostSpotV1TraceConfigSetProductCode = Entry('spot/v1/trace/config/setProductCode', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_user_removetrader = privateSpotPostSpotV1TraceUserRemoveTrader = Entry('spot/v1/trace/user/removeTrader', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_getremovablefollower = privateSpotPostSpotV1TraceGetRemovableFollower = Entry('spot/v1/trace/getRemovableFollower', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_user_removefollower = privateSpotPostSpotV1TraceUserRemoveFollower = Entry('spot/v1/trace/user/removeFollower', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_profit_totalprofitinfo = privateSpotPostSpotV1TraceProfitTotalProfitInfo = Entry('spot/v1/trace/profit/totalProfitInfo', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_profit_totalprofitlist = privateSpotPostSpotV1TraceProfitTotalProfitList = Entry('spot/v1/trace/profit/totalProfitList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_profit_profithislist = privateSpotPostSpotV1TraceProfitProfitHisList = Entry('spot/v1/trace/profit/profitHisList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_profit_profithisdetaillist = privateSpotPostSpotV1TraceProfitProfitHisDetailList = Entry('spot/v1/trace/profit/profitHisDetailList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_profit_waitprofitdetaillist = privateSpotPostSpotV1TraceProfitWaitProfitDetailList = Entry('spot/v1/trace/profit/waitProfitDetailList', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_spot_v1_trace_user_gettraderinfo = privateSpotPostSpotV1TraceUserGetTraderInfo = Entry('spot/v1/trace/user/getTraderInfo', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_trade_place_order = privateSpotPostV2SpotTradePlaceOrder = Entry('v2/spot/trade/place-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_trade_cancel_order = privateSpotPostV2SpotTradeCancelOrder = Entry('v2/spot/trade/cancel-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_trade_batch_orders = privateSpotPostV2SpotTradeBatchOrders = Entry('v2/spot/trade/batch-orders', ['private', 'spot'], 'POST', {'cost': 20})
    private_spot_post_v2_spot_trade_batch_cancel_order = privateSpotPostV2SpotTradeBatchCancelOrder = Entry('v2/spot/trade/batch-cancel-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_trade_cancel_symbol_order = privateSpotPostV2SpotTradeCancelSymbolOrder = Entry('v2/spot/trade/cancel-symbol-order', ['private', 'spot'], 'POST', {'cost': 4})
    private_spot_post_v2_spot_trade_place_plan_order = privateSpotPostV2SpotTradePlacePlanOrder = Entry('v2/spot/trade/place-plan-order', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_v2_spot_trade_modify_plan_order = privateSpotPostV2SpotTradeModifyPlanOrder = Entry('v2/spot/trade/modify-plan-order', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_v2_spot_trade_cancel_plan_order = privateSpotPostV2SpotTradeCancelPlanOrder = Entry('v2/spot/trade/cancel-plan-order', ['private', 'spot'], 'POST', {'cost': 1})
    private_spot_post_v2_spot_trade_batch_cancel_plan_order = privateSpotPostV2SpotTradeBatchCancelPlanOrder = Entry('v2/spot/trade/batch-cancel-plan-order', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_wallet_transfer = privateSpotPostV2SpotWalletTransfer = Entry('v2/spot/wallet/transfer', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_wallet_subaccount_transfer = privateSpotPostV2SpotWalletSubaccountTransfer = Entry('v2/spot/wallet/subaccount-transfer', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_wallet_withdrawal = privateSpotPostV2SpotWalletWithdrawal = Entry('v2/spot/wallet/withdrawal', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_wallet_cancel_withdrawal = privateSpotPostV2SpotWalletCancelWithdrawal = Entry('v2/spot/wallet/cancel-withdrawal', ['private', 'spot'], 'POST', {'cost': 2})
    private_spot_post_v2_spot_wallet_modify_deposit_account = privateSpotPostV2SpotWalletModifyDepositAccount = Entry('v2/spot/wallet/modify-deposit-account', ['private', 'spot'], 'POST', {'cost': 2})
    private_mix_get_mix_v1_account_account = privateMixGetMixV1AccountAccount = Entry('mix/v1/account/account', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_account_accounts = privateMixGetMixV1AccountAccounts = Entry('mix/v1/account/accounts', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_position_singleposition = privateMixGetMixV1PositionSinglePosition = Entry('mix/v1/position/singlePosition', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_position_singleposition_v2 = privateMixGetMixV1PositionSinglePositionV2 = Entry('mix/v1/position/singlePosition-v2', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_position_allposition = privateMixGetMixV1PositionAllPosition = Entry('mix/v1/position/allPosition', ['private', 'mix'], 'GET', {'cost': 4})
    private_mix_get_mix_v1_position_allposition_v2 = privateMixGetMixV1PositionAllPositionV2 = Entry('mix/v1/position/allPosition-v2', ['private', 'mix'], 'GET', {'cost': 4})
    private_mix_get_mix_v1_position_history_position = privateMixGetMixV1PositionHistoryPosition = Entry('mix/v1/position/history-position', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_account_accountbill = privateMixGetMixV1AccountAccountBill = Entry('mix/v1/account/accountBill', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_account_accountbusinessbill = privateMixGetMixV1AccountAccountBusinessBill = Entry('mix/v1/account/accountBusinessBill', ['private', 'mix'], 'GET', {'cost': 4})
    private_mix_get_mix_v1_order_current = privateMixGetMixV1OrderCurrent = Entry('mix/v1/order/current', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_order_margincoincurrent = privateMixGetMixV1OrderMarginCoinCurrent = Entry('mix/v1/order/marginCoinCurrent', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_order_history = privateMixGetMixV1OrderHistory = Entry('mix/v1/order/history', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_order_historyproducttype = privateMixGetMixV1OrderHistoryProductType = Entry('mix/v1/order/historyProductType', ['private', 'mix'], 'GET', {'cost': 4})
    private_mix_get_mix_v1_order_detail = privateMixGetMixV1OrderDetail = Entry('mix/v1/order/detail', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_order_fills = privateMixGetMixV1OrderFills = Entry('mix/v1/order/fills', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_order_allfills = privateMixGetMixV1OrderAllFills = Entry('mix/v1/order/allFills', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_plan_currentplan = privateMixGetMixV1PlanCurrentPlan = Entry('mix/v1/plan/currentPlan', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_plan_historyplan = privateMixGetMixV1PlanHistoryPlan = Entry('mix/v1/plan/historyPlan', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_currenttrack = privateMixGetMixV1TraceCurrentTrack = Entry('mix/v1/trace/currentTrack', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_followerorder = privateMixGetMixV1TraceFollowerOrder = Entry('mix/v1/trace/followerOrder', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_followerhistoryorders = privateMixGetMixV1TraceFollowerHistoryOrders = Entry('mix/v1/trace/followerHistoryOrders', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_historytrack = privateMixGetMixV1TraceHistoryTrack = Entry('mix/v1/trace/historyTrack', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_summary = privateMixGetMixV1TraceSummary = Entry('mix/v1/trace/summary', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_trace_profitsettletokenidgroup = privateMixGetMixV1TraceProfitSettleTokenIdGroup = Entry('mix/v1/trace/profitSettleTokenIdGroup', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_trace_profitdategrouplist = privateMixGetMixV1TraceProfitDateGroupList = Entry('mix/v1/trace/profitDateGroupList', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_trade_profitdatelist = privateMixGetMixV1TradeProfitDateList = Entry('mix/v1/trade/profitDateList', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_waitprofitdatelist = privateMixGetMixV1TraceWaitProfitDateList = Entry('mix/v1/trace/waitProfitDateList', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_trace_tradersymbols = privateMixGetMixV1TraceTraderSymbols = Entry('mix/v1/trace/traderSymbols', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_mix_v1_trace_traderlist = privateMixGetMixV1TraceTraderList = Entry('mix/v1/trace/traderList', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_traderdetail = privateMixGetMixV1TraceTraderDetail = Entry('mix/v1/trace/traderDetail', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_mix_v1_trace_querytraceconfig = privateMixGetMixV1TraceQueryTraceConfig = Entry('mix/v1/trace/queryTraceConfig', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_account_account = privateMixGetV2MixAccountAccount = Entry('v2/mix/account/account', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_account_accounts = privateMixGetV2MixAccountAccounts = Entry('v2/mix/account/accounts', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_account_sub_account_assets = privateMixGetV2MixAccountSubAccountAssets = Entry('v2/mix/account/sub-account-assets', ['private', 'mix'], 'GET', {'cost': 200})
    private_mix_get_v2_mix_account_open_count = privateMixGetV2MixAccountOpenCount = Entry('v2/mix/account/open-count', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_account_bill = privateMixGetV2MixAccountBill = Entry('v2/mix/account/bill', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_market_query_position_lever = privateMixGetV2MixMarketQueryPositionLever = Entry('v2/mix/market/query-position-lever', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_position_single_position = privateMixGetV2MixPositionSinglePosition = Entry('v2/mix/position/single-position', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_position_all_position = privateMixGetV2MixPositionAllPosition = Entry('v2/mix/position/all-position', ['private', 'mix'], 'GET', {'cost': 4})
    private_mix_get_v2_mix_position_history_position = privateMixGetV2MixPositionHistoryPosition = Entry('v2/mix/position/history-position', ['private', 'mix'], 'GET', {'cost': 1})
    private_mix_get_v2_mix_order_detail = privateMixGetV2MixOrderDetail = Entry('v2/mix/order/detail', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_fills = privateMixGetV2MixOrderFills = Entry('v2/mix/order/fills', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_fill_history = privateMixGetV2MixOrderFillHistory = Entry('v2/mix/order/fill-history', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_orders_pending = privateMixGetV2MixOrderOrdersPending = Entry('v2/mix/order/orders-pending', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_orders_history = privateMixGetV2MixOrderOrdersHistory = Entry('v2/mix/order/orders-history', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_orders_plan_pending = privateMixGetV2MixOrderOrdersPlanPending = Entry('v2/mix/order/orders-plan-pending', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_order_orders_plan_history = privateMixGetV2MixOrderOrdersPlanHistory = Entry('v2/mix/order/orders-plan-history', ['private', 'mix'], 'GET', {'cost': 2})
    private_mix_get_v2_mix_market_position_long_short = privateMixGetV2MixMarketPositionLongShort = Entry('v2/mix/market/position-long-short', ['private', 'mix'], 'GET', {'cost': 20})
    private_mix_post_mix_v1_account_sub_account_contract_assets = privateMixPostMixV1AccountSubAccountContractAssets = Entry('mix/v1/account/sub-account-contract-assets', ['private', 'mix'], 'POST', {'cost': 200})
    private_mix_post_mix_v1_account_open_count = privateMixPostMixV1AccountOpenCount = Entry('mix/v1/account/open-count', ['private', 'mix'], 'POST', {'cost': 1})
    private_mix_post_mix_v1_account_setleverage = privateMixPostMixV1AccountSetLeverage = Entry('mix/v1/account/setLeverage', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_mix_v1_account_setmargin = privateMixPostMixV1AccountSetMargin = Entry('mix/v1/account/setMargin', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_mix_v1_account_setmarginmode = privateMixPostMixV1AccountSetMarginMode = Entry('mix/v1/account/setMarginMode', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_mix_v1_account_setpositionmode = privateMixPostMixV1AccountSetPositionMode = Entry('mix/v1/account/setPositionMode', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_mix_v1_order_placeorder = privateMixPostMixV1OrderPlaceOrder = Entry('mix/v1/order/placeOrder', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_batch_orders = privateMixPostMixV1OrderBatchOrders = Entry('mix/v1/order/batch-orders', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_cancel_order = privateMixPostMixV1OrderCancelOrder = Entry('mix/v1/order/cancel-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_cancel_batch_orders = privateMixPostMixV1OrderCancelBatchOrders = Entry('mix/v1/order/cancel-batch-orders', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_modifyorder = privateMixPostMixV1OrderModifyOrder = Entry('mix/v1/order/modifyOrder', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_cancel_symbol_orders = privateMixPostMixV1OrderCancelSymbolOrders = Entry('mix/v1/order/cancel-symbol-orders', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_cancel_all_orders = privateMixPostMixV1OrderCancelAllOrders = Entry('mix/v1/order/cancel-all-orders', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_order_close_all_positions = privateMixPostMixV1OrderCloseAllPositions = Entry('mix/v1/order/close-all-positions', ['private', 'mix'], 'POST', {'cost': 20})
    private_mix_post_mix_v1_plan_placeplan = privateMixPostMixV1PlanPlacePlan = Entry('mix/v1/plan/placePlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_modifyplan = privateMixPostMixV1PlanModifyPlan = Entry('mix/v1/plan/modifyPlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_modifyplanpreset = privateMixPostMixV1PlanModifyPlanPreset = Entry('mix/v1/plan/modifyPlanPreset', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_placetpsl = privateMixPostMixV1PlanPlaceTPSL = Entry('mix/v1/plan/placeTPSL', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_placetrailstop = privateMixPostMixV1PlanPlaceTrailStop = Entry('mix/v1/plan/placeTrailStop', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_placepositionstpsl = privateMixPostMixV1PlanPlacePositionsTPSL = Entry('mix/v1/plan/placePositionsTPSL', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_modifytpslplan = privateMixPostMixV1PlanModifyTPSLPlan = Entry('mix/v1/plan/modifyTPSLPlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_cancelplan = privateMixPostMixV1PlanCancelPlan = Entry('mix/v1/plan/cancelPlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_cancelsymbolplan = privateMixPostMixV1PlanCancelSymbolPlan = Entry('mix/v1/plan/cancelSymbolPlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_plan_cancelallplan = privateMixPostMixV1PlanCancelAllPlan = Entry('mix/v1/plan/cancelAllPlan', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_closetrackorder = privateMixPostMixV1TraceCloseTrackOrder = Entry('mix/v1/trace/closeTrackOrder', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_modifytpsl = privateMixPostMixV1TraceModifyTPSL = Entry('mix/v1/trace/modifyTPSL', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_closetrackorderbysymbol = privateMixPostMixV1TraceCloseTrackOrderBySymbol = Entry('mix/v1/trace/closeTrackOrderBySymbol', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_setupcopysymbols = privateMixPostMixV1TraceSetUpCopySymbols = Entry('mix/v1/trace/setUpCopySymbols', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_followersetbatchtraceconfig = privateMixPostMixV1TraceFollowerSetBatchTraceConfig = Entry('mix/v1/trace/followerSetBatchTraceConfig', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_followerclosebytrackingno = privateMixPostMixV1TraceFollowerCloseByTrackingNo = Entry('mix/v1/trace/followerCloseByTrackingNo', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_followerclosebyall = privateMixPostMixV1TraceFollowerCloseByAll = Entry('mix/v1/trace/followerCloseByAll', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_followersettpsl = privateMixPostMixV1TraceFollowerSetTpsl = Entry('mix/v1/trace/followerSetTpsl', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_cancelcopytrader = privateMixPostMixV1TraceCancelCopyTrader = Entry('mix/v1/trace/cancelCopyTrader', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_mix_v1_trace_traderupdateconfig = privateMixPostMixV1TraceTraderUpdateConfig = Entry('mix/v1/trace/traderUpdateConfig', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_mytraderlist = privateMixPostMixV1TraceMyTraderList = Entry('mix/v1/trace/myTraderList', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_myfollowerlist = privateMixPostMixV1TraceMyFollowerList = Entry('mix/v1/trace/myFollowerList', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_removefollower = privateMixPostMixV1TraceRemoveFollower = Entry('mix/v1/trace/removeFollower', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_public_getfollowerconfig = privateMixPostMixV1TracePublicGetFollowerConfig = Entry('mix/v1/trace/public/getFollowerConfig', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_report_order_historylist = privateMixPostMixV1TraceReportOrderHistoryList = Entry('mix/v1/trace/report/order/historyList', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_report_order_currentlist = privateMixPostMixV1TraceReportOrderCurrentList = Entry('mix/v1/trace/report/order/currentList', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_querytradertpslratioconfig = privateMixPostMixV1TraceQueryTraderTpslRatioConfig = Entry('mix/v1/trace/queryTraderTpslRatioConfig', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_mix_v1_trace_traderupdatetpslratioconfig = privateMixPostMixV1TraceTraderUpdateTpslRatioConfig = Entry('mix/v1/trace/traderUpdateTpslRatioConfig', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_account_set_leverage = privateMixPostV2MixAccountSetLeverage = Entry('v2/mix/account/set-leverage', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_v2_mix_account_set_margin = privateMixPostV2MixAccountSetMargin = Entry('v2/mix/account/set-margin', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_v2_mix_account_set_margin_mode = privateMixPostV2MixAccountSetMarginMode = Entry('v2/mix/account/set-margin-mode', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_v2_mix_account_set_position_mode = privateMixPostV2MixAccountSetPositionMode = Entry('v2/mix/account/set-position-mode', ['private', 'mix'], 'POST', {'cost': 4})
    private_mix_post_v2_mix_order_place_order = privateMixPostV2MixOrderPlaceOrder = Entry('v2/mix/order/place-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_click_backhand = privateMixPostV2MixOrderClickBackhand = Entry('v2/mix/order/click-backhand', ['private', 'mix'], 'POST', {'cost': 20})
    private_mix_post_v2_mix_order_batch_place_order = privateMixPostV2MixOrderBatchPlaceOrder = Entry('v2/mix/order/batch-place-order', ['private', 'mix'], 'POST', {'cost': 20})
    private_mix_post_v2_mix_order_modify_order = privateMixPostV2MixOrderModifyOrder = Entry('v2/mix/order/modify-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_cancel_order = privateMixPostV2MixOrderCancelOrder = Entry('v2/mix/order/cancel-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_batch_cancel_orders = privateMixPostV2MixOrderBatchCancelOrders = Entry('v2/mix/order/batch-cancel-orders', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_close_positions = privateMixPostV2MixOrderClosePositions = Entry('v2/mix/order/close-positions', ['private', 'mix'], 'POST', {'cost': 20})
    private_mix_post_v2_mix_order_place_tpsl_order = privateMixPostV2MixOrderPlaceTpslOrder = Entry('v2/mix/order/place-tpsl-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_place_plan_order = privateMixPostV2MixOrderPlacePlanOrder = Entry('v2/mix/order/place-plan-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_modify_tpsl_order = privateMixPostV2MixOrderModifyTpslOrder = Entry('v2/mix/order/modify-tpsl-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_modify_plan_order = privateMixPostV2MixOrderModifyPlanOrder = Entry('v2/mix/order/modify-plan-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_mix_post_v2_mix_order_cancel_plan_order = privateMixPostV2MixOrderCancelPlanOrder = Entry('v2/mix/order/cancel-plan-order', ['private', 'mix'], 'POST', {'cost': 2})
    private_user_get_user_v1_fee_query = privateUserGetUserV1FeeQuery = Entry('user/v1/fee/query', ['private', 'user'], 'GET', {'cost': 2})
    private_user_get_user_v1_sub_virtual_list = privateUserGetUserV1SubVirtualList = Entry('user/v1/sub/virtual-list', ['private', 'user'], 'GET', {'cost': 2})
    private_user_get_user_v1_sub_virtual_api_list = privateUserGetUserV1SubVirtualApiList = Entry('user/v1/sub/virtual-api-list', ['private', 'user'], 'GET', {'cost': 2})
    private_user_get_user_v1_tax_spot_record = privateUserGetUserV1TaxSpotRecord = Entry('user/v1/tax/spot-record', ['private', 'user'], 'GET', {'cost': 1})
    private_user_get_user_v1_tax_future_record = privateUserGetUserV1TaxFutureRecord = Entry('user/v1/tax/future-record', ['private', 'user'], 'GET', {'cost': 1})
    private_user_get_user_v1_tax_margin_record = privateUserGetUserV1TaxMarginRecord = Entry('user/v1/tax/margin-record', ['private', 'user'], 'GET', {'cost': 1})
    private_user_get_user_v1_tax_p2p_record = privateUserGetUserV1TaxP2pRecord = Entry('user/v1/tax/p2p-record', ['private', 'user'], 'GET', {'cost': 1})
    private_user_get_v2_user_virtual_subaccount_list = privateUserGetV2UserVirtualSubaccountList = Entry('v2/user/virtual-subaccount-list', ['private', 'user'], 'GET', {'cost': 2})
    private_user_get_v2_user_virtual_subaccount_apikey_list = privateUserGetV2UserVirtualSubaccountApikeyList = Entry('v2/user/virtual-subaccount-apikey-list', ['private', 'user'], 'GET', {'cost': 2})
    private_user_post_user_v1_sub_virtual_create = privateUserPostUserV1SubVirtualCreate = Entry('user/v1/sub/virtual-create', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_user_v1_sub_virtual_modify = privateUserPostUserV1SubVirtualModify = Entry('user/v1/sub/virtual-modify', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_user_v1_sub_virtual_api_batch_create = privateUserPostUserV1SubVirtualApiBatchCreate = Entry('user/v1/sub/virtual-api-batch-create', ['private', 'user'], 'POST', {'cost': 20})
    private_user_post_user_v1_sub_virtual_api_create = privateUserPostUserV1SubVirtualApiCreate = Entry('user/v1/sub/virtual-api-create', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_user_v1_sub_virtual_api_modify = privateUserPostUserV1SubVirtualApiModify = Entry('user/v1/sub/virtual-api-modify', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_v2_user_create_virtual_subaccount = privateUserPostV2UserCreateVirtualSubaccount = Entry('v2/user/create-virtual-subaccount', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_v2_user_modify_virtual_subaccount = privateUserPostV2UserModifyVirtualSubaccount = Entry('v2/user/modify-virtual-subaccount', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_v2_user_batch_create_subaccount_and_apikey = privateUserPostV2UserBatchCreateSubaccountAndApikey = Entry('v2/user/batch-create-subaccount-and-apikey', ['private', 'user'], 'POST', {'cost': 20})
    private_user_post_v2_user_create_virtual_subaccount_apikey = privateUserPostV2UserCreateVirtualSubaccountApikey = Entry('v2/user/create-virtual-subaccount-apikey', ['private', 'user'], 'POST', {'cost': 4})
    private_user_post_v2_user_modify_virtual_subaccount_apikey = privateUserPostV2UserModifyVirtualSubaccountApikey = Entry('v2/user/modify-virtual-subaccount-apikey', ['private', 'user'], 'POST', {'cost': 4})
    private_p2p_get_p2p_v1_merchant_merchantlist = privateP2pGetP2pV1MerchantMerchantList = Entry('p2p/v1/merchant/merchantList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_p2p_v1_merchant_merchantinfo = privateP2pGetP2pV1MerchantMerchantInfo = Entry('p2p/v1/merchant/merchantInfo', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_p2p_v1_merchant_advlist = privateP2pGetP2pV1MerchantAdvList = Entry('p2p/v1/merchant/advList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_p2p_v1_merchant_orderlist = privateP2pGetP2pV1MerchantOrderList = Entry('p2p/v1/merchant/orderList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_v2_p2p_merchantlist = privateP2pGetV2P2pMerchantList = Entry('v2/p2p/merchantList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_v2_p2p_merchantinfo = privateP2pGetV2P2pMerchantInfo = Entry('v2/p2p/merchantInfo', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_v2_p2p_orderlist = privateP2pGetV2P2pOrderList = Entry('v2/p2p/orderList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_p2p_get_v2_p2p_advlist = privateP2pGetV2P2pAdvList = Entry('v2/p2p/advList', ['private', 'p2p'], 'GET', {'cost': 2})
    private_broker_get_broker_v1_account_info = privateBrokerGetBrokerV1AccountInfo = Entry('broker/v1/account/info', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_broker_v1_account_sub_list = privateBrokerGetBrokerV1AccountSubList = Entry('broker/v1/account/sub-list', ['private', 'broker'], 'GET', {'cost': 20})
    private_broker_get_broker_v1_account_sub_email = privateBrokerGetBrokerV1AccountSubEmail = Entry('broker/v1/account/sub-email', ['private', 'broker'], 'GET', {'cost': 20})
    private_broker_get_broker_v1_account_sub_spot_assets = privateBrokerGetBrokerV1AccountSubSpotAssets = Entry('broker/v1/account/sub-spot-assets', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_broker_v1_account_sub_future_assets = privateBrokerGetBrokerV1AccountSubFutureAssets = Entry('broker/v1/account/sub-future-assets', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_broker_v1_account_subaccount_transfer = privateBrokerGetBrokerV1AccountSubaccountTransfer = Entry('broker/v1/account/subaccount-transfer', ['private', 'broker'], 'GET', {'cost': 1})
    private_broker_get_broker_v1_account_subaccount_deposit = privateBrokerGetBrokerV1AccountSubaccountDeposit = Entry('broker/v1/account/subaccount-deposit', ['private', 'broker'], 'GET', {'cost': 1})
    private_broker_get_broker_v1_account_subaccount_withdrawal = privateBrokerGetBrokerV1AccountSubaccountWithdrawal = Entry('broker/v1/account/subaccount-withdrawal', ['private', 'broker'], 'GET', {'cost': 1})
    private_broker_get_broker_v1_account_sub_api_list = privateBrokerGetBrokerV1AccountSubApiList = Entry('broker/v1/account/sub-api-list', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_v2_broker_account_info = privateBrokerGetV2BrokerAccountInfo = Entry('v2/broker/account/info', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_v2_broker_account_subaccount_list = privateBrokerGetV2BrokerAccountSubaccountList = Entry('v2/broker/account/subaccount-list', ['private', 'broker'], 'GET', {'cost': 20})
    private_broker_get_v2_broker_account_subaccount_email = privateBrokerGetV2BrokerAccountSubaccountEmail = Entry('v2/broker/account/subaccount-email', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_v2_broker_account_subaccount_spot_assets = privateBrokerGetV2BrokerAccountSubaccountSpotAssets = Entry('v2/broker/account/subaccount-spot-assets', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_v2_broker_account_subaccount_future_assets = privateBrokerGetV2BrokerAccountSubaccountFutureAssets = Entry('v2/broker/account/subaccount-future-assets', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_get_v2_broker_manage_subaccount_apikey_list = privateBrokerGetV2BrokerManageSubaccountApikeyList = Entry('v2/broker/manage/subaccount-apikey-list', ['private', 'broker'], 'GET', {'cost': 2})
    private_broker_post_broker_v1_account_sub_create = privateBrokerPostBrokerV1AccountSubCreate = Entry('broker/v1/account/sub-create', ['private', 'broker'], 'POST', {'cost': 20})
    private_broker_post_broker_v1_account_sub_modify = privateBrokerPostBrokerV1AccountSubModify = Entry('broker/v1/account/sub-modify', ['private', 'broker'], 'POST', {'cost': 20})
    private_broker_post_broker_v1_account_sub_modify_email = privateBrokerPostBrokerV1AccountSubModifyEmail = Entry('broker/v1/account/sub-modify-email', ['private', 'broker'], 'POST', {'cost': 20})
    private_broker_post_broker_v1_account_sub_address = privateBrokerPostBrokerV1AccountSubAddress = Entry('broker/v1/account/sub-address', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_broker_v1_account_sub_withdrawal = privateBrokerPostBrokerV1AccountSubWithdrawal = Entry('broker/v1/account/sub-withdrawal', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_broker_v1_account_sub_auto_transfer = privateBrokerPostBrokerV1AccountSubAutoTransfer = Entry('broker/v1/account/sub-auto-transfer', ['private', 'broker'], 'POST', {'cost': 4})
    private_broker_post_broker_v1_account_sub_api_create = privateBrokerPostBrokerV1AccountSubApiCreate = Entry('broker/v1/account/sub-api-create', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_broker_v1_account_sub_api_modify = privateBrokerPostBrokerV1AccountSubApiModify = Entry('broker/v1/account/sub-api-modify', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_account_modify_subaccount_email = privateBrokerPostV2BrokerAccountModifySubaccountEmail = Entry('v2/broker/account/modify-subaccount-email', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_account_create_subaccount = privateBrokerPostV2BrokerAccountCreateSubaccount = Entry('v2/broker/account/create-subaccount', ['private', 'broker'], 'POST', {'cost': 20})
    private_broker_post_v2_broker_account_modify_subaccount = privateBrokerPostV2BrokerAccountModifySubaccount = Entry('v2/broker/account/modify-subaccount', ['private', 'broker'], 'POST', {'cost': 20})
    private_broker_post_v2_broker_account_subaccount_address = privateBrokerPostV2BrokerAccountSubaccountAddress = Entry('v2/broker/account/subaccount-address', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_account_subaccount_withdrawal = privateBrokerPostV2BrokerAccountSubaccountWithdrawal = Entry('v2/broker/account/subaccount-withdrawal', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_account_set_subaccount_autotransfer = privateBrokerPostV2BrokerAccountSetSubaccountAutotransfer = Entry('v2/broker/account/set-subaccount-autotransfer', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_manage_create_subaccount_apikey = privateBrokerPostV2BrokerManageCreateSubaccountApikey = Entry('v2/broker/manage/create-subaccount-apikey', ['private', 'broker'], 'POST', {'cost': 2})
    private_broker_post_v2_broker_manage_modify_subaccount_apikey = privateBrokerPostV2BrokerManageModifySubaccountApikey = Entry('v2/broker/manage/modify-subaccount-apikey', ['private', 'broker'], 'POST', {'cost': 2})
    private_margin_get_margin_v1_cross_account_riskrate = privateMarginGetMarginV1CrossAccountRiskRate = Entry('margin/v1/cross/account/riskRate', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_account_maxtransferoutamount = privateMarginGetMarginV1CrossAccountMaxTransferOutAmount = Entry('margin/v1/cross/account/maxTransferOutAmount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_account_maxtransferoutamount = privateMarginGetMarginV1IsolatedAccountMaxTransferOutAmount = Entry('margin/v1/isolated/account/maxTransferOutAmount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_order_openorders = privateMarginGetMarginV1IsolatedOrderOpenOrders = Entry('margin/v1/isolated/order/openOrders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_order_history = privateMarginGetMarginV1IsolatedOrderHistory = Entry('margin/v1/isolated/order/history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_order_fills = privateMarginGetMarginV1IsolatedOrderFills = Entry('margin/v1/isolated/order/fills', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_loan_list = privateMarginGetMarginV1IsolatedLoanList = Entry('margin/v1/isolated/loan/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_repay_list = privateMarginGetMarginV1IsolatedRepayList = Entry('margin/v1/isolated/repay/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_interest_list = privateMarginGetMarginV1IsolatedInterestList = Entry('margin/v1/isolated/interest/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_liquidation_list = privateMarginGetMarginV1IsolatedLiquidationList = Entry('margin/v1/isolated/liquidation/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_fin_list = privateMarginGetMarginV1IsolatedFinList = Entry('margin/v1/isolated/fin/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_order_openorders = privateMarginGetMarginV1CrossOrderOpenOrders = Entry('margin/v1/cross/order/openOrders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_order_history = privateMarginGetMarginV1CrossOrderHistory = Entry('margin/v1/cross/order/history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_order_fills = privateMarginGetMarginV1CrossOrderFills = Entry('margin/v1/cross/order/fills', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_loan_list = privateMarginGetMarginV1CrossLoanList = Entry('margin/v1/cross/loan/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_repay_list = privateMarginGetMarginV1CrossRepayList = Entry('margin/v1/cross/repay/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_interest_list = privateMarginGetMarginV1CrossInterestList = Entry('margin/v1/cross/interest/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_liquidation_list = privateMarginGetMarginV1CrossLiquidationList = Entry('margin/v1/cross/liquidation/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_fin_list = privateMarginGetMarginV1CrossFinList = Entry('margin/v1/cross/fin/list', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_cross_account_assets = privateMarginGetMarginV1CrossAccountAssets = Entry('margin/v1/cross/account/assets', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_margin_v1_isolated_account_assets = privateMarginGetMarginV1IsolatedAccountAssets = Entry('margin/v1/isolated/account/assets', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_borrow_history = privateMarginGetV2MarginCrossedBorrowHistory = Entry('v2/margin/crossed/borrow-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_repay_history = privateMarginGetV2MarginCrossedRepayHistory = Entry('v2/margin/crossed/repay-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_interest_history = privateMarginGetV2MarginCrossedInterestHistory = Entry('v2/margin/crossed/interest-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_liquidation_history = privateMarginGetV2MarginCrossedLiquidationHistory = Entry('v2/margin/crossed/liquidation-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_financial_records = privateMarginGetV2MarginCrossedFinancialRecords = Entry('v2/margin/crossed/financial-records', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_account_assets = privateMarginGetV2MarginCrossedAccountAssets = Entry('v2/margin/crossed/account/assets', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_account_risk_rate = privateMarginGetV2MarginCrossedAccountRiskRate = Entry('v2/margin/crossed/account/risk-rate', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_account_max_borrowable_amount = privateMarginGetV2MarginCrossedAccountMaxBorrowableAmount = Entry('v2/margin/crossed/account/max-borrowable-amount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_account_max_transfer_out_amount = privateMarginGetV2MarginCrossedAccountMaxTransferOutAmount = Entry('v2/margin/crossed/account/max-transfer-out-amount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_interest_rate_and_limit = privateMarginGetV2MarginCrossedInterestRateAndLimit = Entry('v2/margin/crossed/interest-rate-and-limit', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_tier_data = privateMarginGetV2MarginCrossedTierData = Entry('v2/margin/crossed/tier-data', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_open_orders = privateMarginGetV2MarginCrossedOpenOrders = Entry('v2/margin/crossed/open-orders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_history_orders = privateMarginGetV2MarginCrossedHistoryOrders = Entry('v2/margin/crossed/history-orders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_crossed_fills = privateMarginGetV2MarginCrossedFills = Entry('v2/margin/crossed/fills', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_borrow_history = privateMarginGetV2MarginIsolatedBorrowHistory = Entry('v2/margin/isolated/borrow-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_repay_history = privateMarginGetV2MarginIsolatedRepayHistory = Entry('v2/margin/isolated/repay-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_interest_history = privateMarginGetV2MarginIsolatedInterestHistory = Entry('v2/margin/isolated/interest-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_liquidation_history = privateMarginGetV2MarginIsolatedLiquidationHistory = Entry('v2/margin/isolated/liquidation-history', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_financial_records = privateMarginGetV2MarginIsolatedFinancialRecords = Entry('v2/margin/isolated/financial-records', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_account_assets = privateMarginGetV2MarginIsolatedAccountAssets = Entry('v2/margin/isolated/account/assets', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_account_risk_rate = privateMarginGetV2MarginIsolatedAccountRiskRate = Entry('v2/margin/isolated/account/risk-rate', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_account_max_borrowable_amount = privateMarginGetV2MarginIsolatedAccountMaxBorrowableAmount = Entry('v2/margin/isolated/account/max-borrowable-amount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_account_max_transfer_out_amount = privateMarginGetV2MarginIsolatedAccountMaxTransferOutAmount = Entry('v2/margin/isolated/account/max-transfer-out-amount', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_interest_rate_and_limit = privateMarginGetV2MarginIsolatedInterestRateAndLimit = Entry('v2/margin/isolated/interest-rate-and-limit', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_tier_data = privateMarginGetV2MarginIsolatedTierData = Entry('v2/margin/isolated/tier-data', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_open_orders = privateMarginGetV2MarginIsolatedOpenOrders = Entry('v2/margin/isolated/open-orders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_history_orders = privateMarginGetV2MarginIsolatedHistoryOrders = Entry('v2/margin/isolated/history-orders', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_get_v2_margin_isolated_fills = privateMarginGetV2MarginIsolatedFills = Entry('v2/margin/isolated/fills', ['private', 'margin'], 'GET', {'cost': 2})
    private_margin_post_margin_v1_cross_account_borrow = privateMarginPostMarginV1CrossAccountBorrow = Entry('margin/v1/cross/account/borrow', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_borrow = privateMarginPostMarginV1IsolatedAccountBorrow = Entry('margin/v1/isolated/account/borrow', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_account_repay = privateMarginPostMarginV1CrossAccountRepay = Entry('margin/v1/cross/account/repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_repay = privateMarginPostMarginV1IsolatedAccountRepay = Entry('margin/v1/isolated/account/repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_riskrate = privateMarginPostMarginV1IsolatedAccountRiskRate = Entry('margin/v1/isolated/account/riskRate', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_account_maxborrowableamount = privateMarginPostMarginV1CrossAccountMaxBorrowableAmount = Entry('margin/v1/cross/account/maxBorrowableAmount', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_maxborrowableamount = privateMarginPostMarginV1IsolatedAccountMaxBorrowableAmount = Entry('margin/v1/isolated/account/maxBorrowableAmount', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_flashrepay = privateMarginPostMarginV1IsolatedAccountFlashRepay = Entry('margin/v1/isolated/account/flashRepay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_account_queryflashrepaystatus = privateMarginPostMarginV1IsolatedAccountQueryFlashRepayStatus = Entry('margin/v1/isolated/account/queryFlashRepayStatus', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_account_flashrepay = privateMarginPostMarginV1CrossAccountFlashRepay = Entry('margin/v1/cross/account/flashRepay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_account_queryflashrepaystatus = privateMarginPostMarginV1CrossAccountQueryFlashRepayStatus = Entry('margin/v1/cross/account/queryFlashRepayStatus', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_order_placeorder = privateMarginPostMarginV1IsolatedOrderPlaceOrder = Entry('margin/v1/isolated/order/placeOrder', ['private', 'margin'], 'POST', {'cost': 4})
    private_margin_post_margin_v1_isolated_order_batchplaceorder = privateMarginPostMarginV1IsolatedOrderBatchPlaceOrder = Entry('margin/v1/isolated/order/batchPlaceOrder', ['private', 'margin'], 'POST', {'cost': 4})
    private_margin_post_margin_v1_isolated_order_cancelorder = privateMarginPostMarginV1IsolatedOrderCancelOrder = Entry('margin/v1/isolated/order/cancelOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_isolated_order_batchcancelorder = privateMarginPostMarginV1IsolatedOrderBatchCancelOrder = Entry('margin/v1/isolated/order/batchCancelOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_order_placeorder = privateMarginPostMarginV1CrossOrderPlaceOrder = Entry('margin/v1/cross/order/placeOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_order_batchplaceorder = privateMarginPostMarginV1CrossOrderBatchPlaceOrder = Entry('margin/v1/cross/order/batchPlaceOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_order_cancelorder = privateMarginPostMarginV1CrossOrderCancelOrder = Entry('margin/v1/cross/order/cancelOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_margin_v1_cross_order_batchcancelorder = privateMarginPostMarginV1CrossOrderBatchCancelOrder = Entry('margin/v1/cross/order/batchCancelOrder', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_account_borrow = privateMarginPostV2MarginCrossedAccountBorrow = Entry('v2/margin/crossed/account/borrow', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_account_repay = privateMarginPostV2MarginCrossedAccountRepay = Entry('v2/margin/crossed/account/repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_account_flash_repay = privateMarginPostV2MarginCrossedAccountFlashRepay = Entry('v2/margin/crossed/account/flash-repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_account_query_flash_repay_status = privateMarginPostV2MarginCrossedAccountQueryFlashRepayStatus = Entry('v2/margin/crossed/account/query-flash-repay-status', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_place_order = privateMarginPostV2MarginCrossedPlaceOrder = Entry('v2/margin/crossed/place-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_batch_place_order = privateMarginPostV2MarginCrossedBatchPlaceOrder = Entry('v2/margin/crossed/batch-place-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_cancel_order = privateMarginPostV2MarginCrossedCancelOrder = Entry('v2/margin/crossed/cancel-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_crossed_batch_cancel_order = privateMarginPostV2MarginCrossedBatchCancelOrder = Entry('v2/margin/crossed/batch-cancel-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_account_borrow = privateMarginPostV2MarginIsolatedAccountBorrow = Entry('v2/margin/isolated/account/borrow', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_account_repay = privateMarginPostV2MarginIsolatedAccountRepay = Entry('v2/margin/isolated/account/repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_account_flash_repay = privateMarginPostV2MarginIsolatedAccountFlashRepay = Entry('v2/margin/isolated/account/flash-repay', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_account_query_flash_repay_status = privateMarginPostV2MarginIsolatedAccountQueryFlashRepayStatus = Entry('v2/margin/isolated/account/query-flash-repay-status', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_place_order = privateMarginPostV2MarginIsolatedPlaceOrder = Entry('v2/margin/isolated/place-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_batch_place_order = privateMarginPostV2MarginIsolatedBatchPlaceOrder = Entry('v2/margin/isolated/batch-place-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_cancel_order = privateMarginPostV2MarginIsolatedCancelOrder = Entry('v2/margin/isolated/cancel-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_margin_post_v2_margin_isolated_batch_cancel_order = privateMarginPostV2MarginIsolatedBatchCancelOrder = Entry('v2/margin/isolated/batch-cancel-order', ['private', 'margin'], 'POST', {'cost': 2})
    private_copy_get_v2_copy_mix_trader_order_current_track = privateCopyGetV2CopyMixTraderOrderCurrentTrack = Entry('v2/copy/mix-trader/order-current-track', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_trader_order_history_track = privateCopyGetV2CopyMixTraderOrderHistoryTrack = Entry('v2/copy/mix-trader/order-history-track', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_trader_order_total_detail = privateCopyGetV2CopyMixTraderOrderTotalDetail = Entry('v2/copy/mix-trader/order-total-detail', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_trader_profit_history_summarys = privateCopyGetV2CopyMixTraderProfitHistorySummarys = Entry('v2/copy/mix-trader/profit-history-summarys', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_trader_profit_history_details = privateCopyGetV2CopyMixTraderProfitHistoryDetails = Entry('v2/copy/mix-trader/profit-history-details', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_trader_profit_details = privateCopyGetV2CopyMixTraderProfitDetails = Entry('v2/copy/mix-trader/profit-details', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_trader_profits_group_coin_date = privateCopyGetV2CopyMixTraderProfitsGroupCoinDate = Entry('v2/copy/mix-trader/profits-group-coin-date', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_trader_config_query_symbols = privateCopyGetV2CopyMixTraderConfigQuerySymbols = Entry('v2/copy/mix-trader/config-query-symbols', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_trader_config_query_followers = privateCopyGetV2CopyMixTraderConfigQueryFollowers = Entry('v2/copy/mix-trader/config-query-followers', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_follower_query_current_orders = privateCopyGetV2CopyMixFollowerQueryCurrentOrders = Entry('v2/copy/mix-follower/query-current-orders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_follower_query_history_orders = privateCopyGetV2CopyMixFollowerQueryHistoryOrders = Entry('v2/copy/mix-follower/query-history-orders', ['private', 'copy'], 'GET', {'cost': 1})
    private_copy_get_v2_copy_mix_follower_query_settings = privateCopyGetV2CopyMixFollowerQuerySettings = Entry('v2/copy/mix-follower/query-settings', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_follower_query_traders = privateCopyGetV2CopyMixFollowerQueryTraders = Entry('v2/copy/mix-follower/query-traders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_follower_query_quantity_limit = privateCopyGetV2CopyMixFollowerQueryQuantityLimit = Entry('v2/copy/mix-follower/query-quantity-limit', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_broker_query_traders = privateCopyGetV2CopyMixBrokerQueryTraders = Entry('v2/copy/mix-broker/query-traders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_broker_query_history_traces = privateCopyGetV2CopyMixBrokerQueryHistoryTraces = Entry('v2/copy/mix-broker/query-history-traces', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_mix_broker_query_current_traces = privateCopyGetV2CopyMixBrokerQueryCurrentTraces = Entry('v2/copy/mix-broker/query-current-traces', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_profit_summarys = privateCopyGetV2CopySpotTraderProfitSummarys = Entry('v2/copy/spot-trader/profit-summarys', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_profit_history_details = privateCopyGetV2CopySpotTraderProfitHistoryDetails = Entry('v2/copy/spot-trader/profit-history-details', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_profit_details = privateCopyGetV2CopySpotTraderProfitDetails = Entry('v2/copy/spot-trader/profit-details', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_order_total_detail = privateCopyGetV2CopySpotTraderOrderTotalDetail = Entry('v2/copy/spot-trader/order-total-detail', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_order_history_track = privateCopyGetV2CopySpotTraderOrderHistoryTrack = Entry('v2/copy/spot-trader/order-history-track', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_order_current_track = privateCopyGetV2CopySpotTraderOrderCurrentTrack = Entry('v2/copy/spot-trader/order-current-track', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_config_query_settings = privateCopyGetV2CopySpotTraderConfigQuerySettings = Entry('v2/copy/spot-trader/config-query-settings', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_trader_config_query_followers = privateCopyGetV2CopySpotTraderConfigQueryFollowers = Entry('v2/copy/spot-trader/config-query-followers', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_follower_query_traders = privateCopyGetV2CopySpotFollowerQueryTraders = Entry('v2/copy/spot-follower/query-traders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_follower_query_trader_symbols = privateCopyGetV2CopySpotFollowerQueryTraderSymbols = Entry('v2/copy/spot-follower/query-trader-symbols', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_follower_query_settings = privateCopyGetV2CopySpotFollowerQuerySettings = Entry('v2/copy/spot-follower/query-settings', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_follower_query_history_orders = privateCopyGetV2CopySpotFollowerQueryHistoryOrders = Entry('v2/copy/spot-follower/query-history-orders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_get_v2_copy_spot_follower_query_current_orders = privateCopyGetV2CopySpotFollowerQueryCurrentOrders = Entry('v2/copy/spot-follower/query-current-orders', ['private', 'copy'], 'GET', {'cost': 2})
    private_copy_post_v2_copy_mix_trader_order_modify_tpsl = privateCopyPostV2CopyMixTraderOrderModifyTpsl = Entry('v2/copy/mix-trader/order-modify-tpsl', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_trader_order_close_positions = privateCopyPostV2CopyMixTraderOrderClosePositions = Entry('v2/copy/mix-trader/order-close-positions', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_trader_config_setting_symbols = privateCopyPostV2CopyMixTraderConfigSettingSymbols = Entry('v2/copy/mix-trader/config-setting-symbols', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_trader_config_setting_base = privateCopyPostV2CopyMixTraderConfigSettingBase = Entry('v2/copy/mix-trader/config-setting-base', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_trader_config_remove_follower = privateCopyPostV2CopyMixTraderConfigRemoveFollower = Entry('v2/copy/mix-trader/config-remove-follower', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_follower_setting_tpsl = privateCopyPostV2CopyMixFollowerSettingTpsl = Entry('v2/copy/mix-follower/setting-tpsl', ['private', 'copy'], 'POST', {'cost': 1})
    private_copy_post_v2_copy_mix_follower_settings = privateCopyPostV2CopyMixFollowerSettings = Entry('v2/copy/mix-follower/settings', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_follower_close_positions = privateCopyPostV2CopyMixFollowerClosePositions = Entry('v2/copy/mix-follower/close-positions', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_mix_follower_cancel_trader = privateCopyPostV2CopyMixFollowerCancelTrader = Entry('v2/copy/mix-follower/cancel-trader', ['private', 'copy'], 'POST', {'cost': 4})
    private_copy_post_v2_copy_spot_trader_order_modify_tpsl = privateCopyPostV2CopySpotTraderOrderModifyTpsl = Entry('v2/copy/spot-trader/order-modify-tpsl', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_trader_order_close_tracking = privateCopyPostV2CopySpotTraderOrderCloseTracking = Entry('v2/copy/spot-trader/order-close-tracking', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_trader_config_setting_symbols = privateCopyPostV2CopySpotTraderConfigSettingSymbols = Entry('v2/copy/spot-trader/config-setting-symbols', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_trader_config_remove_follower = privateCopyPostV2CopySpotTraderConfigRemoveFollower = Entry('v2/copy/spot-trader/config-remove-follower', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_follower_stop_order = privateCopyPostV2CopySpotFollowerStopOrder = Entry('v2/copy/spot-follower/stop-order', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_follower_settings = privateCopyPostV2CopySpotFollowerSettings = Entry('v2/copy/spot-follower/settings', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_follower_setting_tpsl = privateCopyPostV2CopySpotFollowerSettingTpsl = Entry('v2/copy/spot-follower/setting-tpsl', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_follower_order_close_tracking = privateCopyPostV2CopySpotFollowerOrderCloseTracking = Entry('v2/copy/spot-follower/order-close-tracking', ['private', 'copy'], 'POST', {'cost': 2})
    private_copy_post_v2_copy_spot_follower_cancel_trader = privateCopyPostV2CopySpotFollowerCancelTrader = Entry('v2/copy/spot-follower/cancel-trader', ['private', 'copy'], 'POST', {'cost': 2})
    private_tax_get_v2_tax_spot_record = privateTaxGetV2TaxSpotRecord = Entry('v2/tax/spot-record', ['private', 'tax'], 'GET', {'cost': 20})
    private_tax_get_v2_tax_future_record = privateTaxGetV2TaxFutureRecord = Entry('v2/tax/future-record', ['private', 'tax'], 'GET', {'cost': 20})
    private_tax_get_v2_tax_margin_record = privateTaxGetV2TaxMarginRecord = Entry('v2/tax/margin-record', ['private', 'tax'], 'GET', {'cost': 20})
    private_tax_get_v2_tax_p2p_record = privateTaxGetV2TaxP2pRecord = Entry('v2/tax/p2p-record', ['private', 'tax'], 'GET', {'cost': 20})
    private_convert_get_v2_convert_currencies = privateConvertGetV2ConvertCurrencies = Entry('v2/convert/currencies', ['private', 'convert'], 'GET', {'cost': 2})
    private_convert_get_v2_convert_quoted_price = privateConvertGetV2ConvertQuotedPrice = Entry('v2/convert/quoted-price', ['private', 'convert'], 'GET', {'cost': 2})
    private_convert_get_v2_convert_convert_record = privateConvertGetV2ConvertConvertRecord = Entry('v2/convert/convert-record', ['private', 'convert'], 'GET', {'cost': 2})
    private_convert_get_v2_convert_bgb_convert_coin_list = privateConvertGetV2ConvertBgbConvertCoinList = Entry('v2/convert/bgb-convert-coin-list', ['private', 'convert'], 'GET', {'cost': 2})
    private_convert_get_v2_convert_bgb_convert_records = privateConvertGetV2ConvertBgbConvertRecords = Entry('v2/convert/bgb-convert-records', ['private', 'convert'], 'GET', {'cost': 2})
    private_convert_post_v2_convert_trade = privateConvertPostV2ConvertTrade = Entry('v2/convert/trade', ['private', 'convert'], 'POST', {'cost': 2})
    private_convert_post_v2_convert_bgb_convert = privateConvertPostV2ConvertBgbConvert = Entry('v2/convert/bgb-convert', ['private', 'convert'], 'POST', {'cost': 2})
    private_earn_get_v2_earn_savings_product = privateEarnGetV2EarnSavingsProduct = Entry('v2/earn/savings/product', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_account = privateEarnGetV2EarnSavingsAccount = Entry('v2/earn/savings/account', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_assets = privateEarnGetV2EarnSavingsAssets = Entry('v2/earn/savings/assets', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_records = privateEarnGetV2EarnSavingsRecords = Entry('v2/earn/savings/records', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_subscribe_info = privateEarnGetV2EarnSavingsSubscribeInfo = Entry('v2/earn/savings/subscribe-info', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_subscribe_result = privateEarnGetV2EarnSavingsSubscribeResult = Entry('v2/earn/savings/subscribe-result', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_savings_redeem_result = privateEarnGetV2EarnSavingsRedeemResult = Entry('v2/earn/savings/redeem-result', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_product = privateEarnGetV2EarnSharkfinProduct = Entry('v2/earn/sharkfin/product', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_account = privateEarnGetV2EarnSharkfinAccount = Entry('v2/earn/sharkfin/account', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_assets = privateEarnGetV2EarnSharkfinAssets = Entry('v2/earn/sharkfin/assets', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_records = privateEarnGetV2EarnSharkfinRecords = Entry('v2/earn/sharkfin/records', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_subscribe_info = privateEarnGetV2EarnSharkfinSubscribeInfo = Entry('v2/earn/sharkfin/subscribe-info', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_sharkfin_subscribe_result = privateEarnGetV2EarnSharkfinSubscribeResult = Entry('v2/earn/sharkfin/subscribe-result', ['private', 'earn'], 'GET', {'cost': 4})
    private_earn_get_v2_earn_loan_ongoing_orders = privateEarnGetV2EarnLoanOngoingOrders = Entry('v2/earn/loan/ongoing-orders', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_loan_repay_history = privateEarnGetV2EarnLoanRepayHistory = Entry('v2/earn/loan/repay-history', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_loan_revise_history = privateEarnGetV2EarnLoanReviseHistory = Entry('v2/earn/loan/revise-history', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_loan_borrow_history = privateEarnGetV2EarnLoanBorrowHistory = Entry('v2/earn/loan/borrow-history', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_loan_debts = privateEarnGetV2EarnLoanDebts = Entry('v2/earn/loan/debts', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_loan_reduces = privateEarnGetV2EarnLoanReduces = Entry('v2/earn/loan/reduces', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_get_v2_earn_account_assets = privateEarnGetV2EarnAccountAssets = Entry('v2/earn/account/assets', ['private', 'earn'], 'GET', {'cost': 2})
    private_earn_post_v2_earn_savings_subscribe = privateEarnPostV2EarnSavingsSubscribe = Entry('v2/earn/savings/subscribe', ['private', 'earn'], 'POST', {'cost': 2})
    private_earn_post_v2_earn_savings_redeem = privateEarnPostV2EarnSavingsRedeem = Entry('v2/earn/savings/redeem', ['private', 'earn'], 'POST', {'cost': 2})
    private_earn_post_v2_earn_sharkfin_subscribe = privateEarnPostV2EarnSharkfinSubscribe = Entry('v2/earn/sharkfin/subscribe', ['private', 'earn'], 'POST', {'cost': 2})
    private_earn_post_v2_earn_loan_borrow = privateEarnPostV2EarnLoanBorrow = Entry('v2/earn/loan/borrow', ['private', 'earn'], 'POST', {'cost': 2})
    private_earn_post_v2_earn_loan_repay = privateEarnPostV2EarnLoanRepay = Entry('v2/earn/loan/repay', ['private', 'earn'], 'POST', {'cost': 2})
    private_earn_post_v2_earn_loan_revise_pledge = privateEarnPostV2EarnLoanRevisePledge = Entry('v2/earn/loan/revise-pledge', ['private', 'earn'], 'POST', {'cost': 2})
    private_common_get_v2_common_trade_rate = privateCommonGetV2CommonTradeRate = Entry('v2/common/trade-rate', ['private', 'common'], 'GET', {'cost': 2})
