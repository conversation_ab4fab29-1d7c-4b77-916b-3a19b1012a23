from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_market_tickers = publicGetMarketTickers = Entry('market/tickers', 'public', 'GET', {'cost': 1})
    public_get_market_ticker = publicGetMarketTicker = Entry('market/ticker', 'public', 'GET', {'cost': 1})
    public_get_market_books = publicGetMarketBooks = Entry('market/books', 'public', 'GET', {'cost': 0.5})
    public_get_market_candles = publicGetMarketCandles = Entry('market/candles', 'public', 'GET', {'cost': 0.5})
    public_get_market_history_candles = publicGetMarketHistoryCandles = Entry('market/history-candles', 'public', 'GET', {'cost': 0.5})
    public_get_market_trades = publicGetMarketTrades = Entry('market/trades', 'public', 'GET', {'cost': 0.2})
    public_get_market_history_trades = publicGetMarketHistoryTrades = Entry('market/history-trades', 'public', 'GET', {'cost': 2})
    public_get_market_platform_24_volume = publicGetMarketPlatform24Volume = Entry('market/platform-24-volume', 'public', 'GET', {'cost': 10})
    public_get_market_open_oracle = publicGetMarketOpenOracle = Entry('market/open-oracle', 'public', 'GET', {'cost': 50})
    public_get_market_exchange_rate = publicGetMarketExchangeRate = Entry('market/exchange-rate', 'public', 'GET', {'cost': 20})
    public_get_public_instruments = publicGetPublicInstruments = Entry('public/instruments', 'public', 'GET', {'cost': 1})
    public_get_public_time = publicGetPublicTime = Entry('public/time', 'public', 'GET', {'cost': 2})
    private_get_trade_order = privateGetTradeOrder = Entry('trade/order', 'private', 'GET', {'cost': 0.****************})
    private_get_trade_orders_pending = privateGetTradeOrdersPending = Entry('trade/orders-pending', 'private', 'GET', {'cost': 0.****************})
    private_get_trade_orders_history = privateGetTradeOrdersHistory = Entry('trade/orders-history', 'private', 'GET', {'cost': 0.5})
    private_get_trade_orders_history_archive = privateGetTradeOrdersHistoryArchive = Entry('trade/orders-history-archive', 'private', 'GET', {'cost': 0.5})
    private_get_trade_fills = privateGetTradeFills = Entry('trade/fills', 'private', 'GET', {'cost': 0.****************})
    private_get_trade_fills_history = privateGetTradeFillsHistory = Entry('trade/fills-history', 'private', 'GET', {'cost': 2.2})
    private_get_trade_fills_archive = privateGetTradeFillsArchive = Entry('trade/fills-archive', 'private', 'GET', {'cost': 2})
    private_get_trade_order_algo = privateGetTradeOrderAlgo = Entry('trade/order-algo', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_pending = privateGetTradeOrdersAlgoPending = Entry('trade/orders-algo-pending', 'private', 'GET', {'cost': 1})
    private_get_trade_orders_algo_history = privateGetTradeOrdersAlgoHistory = Entry('trade/orders-algo-history', 'private', 'GET', {'cost': 1})
    private_get_otc_rfq_trade = privateGetOtcRfqTrade = Entry('otc/rfq/trade', 'private', 'GET', {'cost': 4})
    private_get_otc_rfq_history = privateGetOtcRfqHistory = Entry('otc/rfq/history', 'private', 'GET', {'cost': 4})
    private_get_account_balance = privateGetAccountBalance = Entry('account/balance', 'private', 'GET', {'cost': 2})
    private_get_account_bills = privateGetAccountBills = Entry('account/bills', 'private', 'GET', {'cost': 1.***************7})
    private_get_account_bills_archive = privateGetAccountBillsArchive = Entry('account/bills-archive', 'private', 'GET', {'cost': 1.***************7})
    private_get_account_config = privateGetAccountConfig = Entry('account/config', 'private', 'GET', {'cost': 4})
    private_get_account_max_size = privateGetAccountMaxSize = Entry('account/max-size', 'private', 'GET', {'cost': 4})
    private_get_account_max_avail_size = privateGetAccountMaxAvailSize = Entry('account/max-avail-size', 'private', 'GET', {'cost': 4})
    private_get_account_trade_fee = privateGetAccountTradeFee = Entry('account/trade-fee', 'private', 'GET', {'cost': 4})
    private_get_account_max_withdrawal = privateGetAccountMaxWithdrawal = Entry('account/max-withdrawal', 'private', 'GET', {'cost': 4})
    private_get_asset_currencies = privateGetAssetCurrencies = Entry('asset/currencies', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_balances = privateGetAssetBalances = Entry('asset/balances', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_asset_valuation = privateGetAssetAssetValuation = Entry('asset/asset-valuation', 'private', 'GET', {'cost': 10})
    private_get_asset_transfer_state = privateGetAssetTransferState = Entry('asset/transfer-state', 'private', 'GET', {'cost': 10})
    private_get_asset_bills = privateGetAssetBills = Entry('asset/bills', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_deposit_lightning = privateGetAssetDepositLightning = Entry('asset/deposit-lightning', 'private', 'GET', {'cost': 5})
    private_get_asset_deposit_address = privateGetAssetDepositAddress = Entry('asset/deposit-address', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_deposit_history = privateGetAssetDepositHistory = Entry('asset/deposit-history', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_withdrawal_history = privateGetAssetWithdrawalHistory = Entry('asset/withdrawal-history', 'private', 'GET', {'cost': 1.***************7})
    private_get_asset_deposit_withdraw_status = privateGetAssetDepositWithdrawStatus = Entry('asset/deposit-withdraw-status', 'private', 'GET', {'cost': 20})
    private_get_fiat_deposit_history = privateGetFiatDepositHistory = Entry('fiat/deposit-history', 'private', 'GET', {'cost': 1.***************7})
    private_get_fiat_withdraw_history = privateGetFiatWithdrawHistory = Entry('fiat-withdraw-history', 'private', 'GET', {'cost': 1.***************7})
    private_get_fiat_channel = privateGetFiatChannel = Entry('fiat-channel', 'private', 'GET', {'cost': 1.***************7})
    private_get_users_subaccount_list = privateGetUsersSubaccountList = Entry('users/subaccount/list', 'private', 'GET', {'cost': 10})
    private_get_users_subaccount_apikey = privateGetUsersSubaccountApiKey = Entry('users/subaccount/apiKey', 'private', 'GET', {'cost': 10})
    private_get_account_subaccount_balances = privateGetAccountSubaccountBalances = Entry('account/subaccount/balances', 'private', 'GET', {'cost': 10})
    private_get_asset_subaccount_balances = privateGetAssetSubaccountBalances = Entry('asset/subaccount/balances', 'private', 'GET', {'cost': 10})
    private_get_asset_subaccount_bills = privateGetAssetSubaccountBills = Entry('asset/subaccount/bills', 'private', 'GET', {'cost': 10})
    private_post_trade_order = privatePostTradeOrder = Entry('trade/order', 'private', 'POST', {'cost': 0.****************})
    private_post_trade_batch_orders = privatePostTradeBatchOrders = Entry('trade/batch-orders', 'private', 'POST', {'cost': 0.0***************7})
    private_post_trade_cancel_order = privatePostTradeCancelOrder = Entry('trade/cancel-order', 'private', 'POST', {'cost': 0.****************})
    private_post_trade_cancel_batch_orders = privatePostTradeCancelBatchOrders = Entry('trade/cancel-batch-orders', 'private', 'POST', {'cost': 0.0***************7})
    private_post_trade_amend_order = privatePostTradeAmendOrder = Entry('trade/amend-order', 'private', 'POST', {'cost': 0.****************})
    private_post_trade_amend_batch_orders = privatePostTradeAmendBatchOrders = Entry('trade/amend-batch-orders', 'private', 'POST', {'cost': 0.00***************7})
    private_post_trade_order_algo = privatePostTradeOrderAlgo = Entry('trade/order-algo', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_algos = privatePostTradeCancelAlgos = Entry('trade/cancel-algos', 'private', 'POST', {'cost': 1})
    private_post_trade_cancel_advance_algos = privatePostTradeCancelAdvanceAlgos = Entry('trade/cancel-advance-algos', 'private', 'POST', {'cost': 1})
    private_post_otc_rfq_quote = privatePostOtcRfqQuote = Entry('otc/rfq/quote', 'private', 'POST', {'cost': 4})
    private_post_otc_rfq_trade = privatePostOtcRfqTrade = Entry('otc/rfq/trade', 'private', 'POST', {'cost': 4})
    private_post_asset_transfer = privatePostAssetTransfer = Entry('asset/transfer', 'private', 'POST', {'cost': 4})
    private_post_asset_withdrawal = privatePostAssetWithdrawal = Entry('asset/withdrawal', 'private', 'POST', {'cost': 4})
    private_post_asset_withdrawal_lightning = privatePostAssetWithdrawalLightning = Entry('asset/withdrawal-lightning', 'private', 'POST', {'cost': 4})
    private_post_asset_withdrawal_cancel = privatePostAssetWithdrawalCancel = Entry('asset/withdrawal-cancel', 'private', 'POST', {'cost': 4})
    private_post_fiat_deposit = privatePostFiatDeposit = Entry('fiat/deposit', 'private', 'POST', {'cost': 1.***************7})
    private_post_fiat_cancel_deposit = privatePostFiatCancelDeposit = Entry('fiat/cancel-deposit', 'private', 'POST', {'cost': 1.***************7})
    private_post_fiat_withdrawal = privatePostFiatWithdrawal = Entry('fiat/withdrawal', 'private', 'POST', {'cost': 1.***************7})
    private_post_fiat_cancel_withdrawal = privatePostFiatCancelWithdrawal = Entry('fiat/cancel-withdrawal', 'private', 'POST', {'cost': 1.***************7})
    private_post_asset_subaccount_transfer = privatePostAssetSubaccountTransfer = Entry('asset/subaccount/transfer', 'private', 'POST', {'cost': 10})
