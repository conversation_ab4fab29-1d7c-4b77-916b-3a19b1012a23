from ccxt.base.types import Entry


class ImplicitAPI:
    matcher_get_matcher = matcherGetMatcher = Entry('matcher', 'matcher', 'GET', {})
    matcher_get_matcher_settings = matcherGetMatcherSettings = Entry('matcher/settings', 'matcher', 'GET', {})
    matcher_get_matcher_settings_rates = matcherGetMatcherSettingsRates = Entry('matcher/settings/rates', 'matcher', 'GET', {})
    matcher_get_matcher_balance_reserved_publickey = matcherGetMatcherBalanceReservedPublicKey = Entry('matcher/balance/reserved/{publicKey}', 'matcher', 'GET', {})
    matcher_get_matcher_debug_allsnashotoffsets = matcherGetMatcherDebugAllSnashotOffsets = Entry('matcher/debug/allSnashotOffsets', 'matcher', 'GET', {})
    matcher_get_matcher_debug_currentoffset = matcherGetMatcherDebugCurrentOffset = Entry('matcher/debug/currentOffset', 'matcher', 'GET', {})
    matcher_get_matcher_debug_lastoffset = matcherGetMatcherDebugLastOffset = Entry('matcher/debug/lastOffset', 'matcher', 'GET', {})
    matcher_get_matcher_debug_oldestsnapshotoffset = matcherGetMatcherDebugOldestSnapshotOffset = Entry('matcher/debug/oldestSnapshotOffset', 'matcher', 'GET', {})
    matcher_get_matcher_debug_config = matcherGetMatcherDebugConfig = Entry('matcher/debug/config', 'matcher', 'GET', {})
    matcher_get_matcher_debug_address_address = matcherGetMatcherDebugAddressAddress = Entry('matcher/debug/address/{address}', 'matcher', 'GET', {})
    matcher_get_matcher_debug_status = matcherGetMatcherDebugStatus = Entry('matcher/debug/status', 'matcher', 'GET', {})
    matcher_get_matcher_debug_address_address_check = matcherGetMatcherDebugAddressAddressCheck = Entry('matcher/debug/address/{address}/check', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook = matcherGetMatcherOrderbook = Entry('matcher/orderbook', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid = matcherGetMatcherOrderbookBaseIdQuoteId = Entry('matcher/orderbook/{baseId}/{quoteId}', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid_publickey_publickey = matcherGetMatcherOrderbookBaseIdQuoteIdPublicKeyPublicKey = Entry('matcher/orderbook/{baseId}/{quoteId}/publicKey/{publicKey}', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid_orderid = matcherGetMatcherOrderbookBaseIdQuoteIdOrderId = Entry('matcher/orderbook/{baseId}/{quoteId}/{orderId}', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid_info = matcherGetMatcherOrderbookBaseIdQuoteIdInfo = Entry('matcher/orderbook/{baseId}/{quoteId}/info', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid_status = matcherGetMatcherOrderbookBaseIdQuoteIdStatus = Entry('matcher/orderbook/{baseId}/{quoteId}/status', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_baseid_quoteid_tradablebalance_address = matcherGetMatcherOrderbookBaseIdQuoteIdTradableBalanceAddress = Entry('matcher/orderbook/{baseId}/{quoteId}/tradableBalance/{address}', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_publickey = matcherGetMatcherOrderbookPublicKey = Entry('matcher/orderbook/{publicKey}', 'matcher', 'GET', {})
    matcher_get_matcher_orderbook_publickey_orderid = matcherGetMatcherOrderbookPublicKeyOrderId = Entry('matcher/orderbook/{publicKey}/{orderId}', 'matcher', 'GET', {})
    matcher_get_matcher_orders_address = matcherGetMatcherOrdersAddress = Entry('matcher/orders/{address}', 'matcher', 'GET', {})
    matcher_get_matcher_orders_address_orderid = matcherGetMatcherOrdersAddressOrderId = Entry('matcher/orders/{address}/{orderId}', 'matcher', 'GET', {})
    matcher_get_matcher_transactions_orderid = matcherGetMatcherTransactionsOrderId = Entry('matcher/transactions/{orderId}', 'matcher', 'GET', {})
    matcher_get_api_v1_orderbook_baseid_quoteid = matcherGetApiV1OrderbookBaseIdQuoteId = Entry('api/v1/orderbook/{baseId}/{quoteId}', 'matcher', 'GET', {})
    matcher_post_matcher_orderbook = matcherPostMatcherOrderbook = Entry('matcher/orderbook', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_market = matcherPostMatcherOrderbookMarket = Entry('matcher/orderbook/market', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_cancel = matcherPostMatcherOrderbookCancel = Entry('matcher/orderbook/cancel', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_baseid_quoteid_cancel = matcherPostMatcherOrderbookBaseIdQuoteIdCancel = Entry('matcher/orderbook/{baseId}/{quoteId}/cancel', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_baseid_quoteid_calculatefee = matcherPostMatcherOrderbookBaseIdQuoteIdCalculateFee = Entry('matcher/orderbook/{baseId}/{quoteId}/calculateFee', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_baseid_quoteid_delete = matcherPostMatcherOrderbookBaseIdQuoteIdDelete = Entry('matcher/orderbook/{baseId}/{quoteId}/delete', 'matcher', 'POST', {})
    matcher_post_matcher_orderbook_baseid_quoteid_cancelall = matcherPostMatcherOrderbookBaseIdQuoteIdCancelAll = Entry('matcher/orderbook/{baseId}/{quoteId}/cancelAll', 'matcher', 'POST', {})
    matcher_post_matcher_debug_savesnapshots = matcherPostMatcherDebugSaveSnapshots = Entry('matcher/debug/saveSnapshots', 'matcher', 'POST', {})
    matcher_post_matcher_orders_address_cancel = matcherPostMatcherOrdersAddressCancel = Entry('matcher/orders/{address}/cancel', 'matcher', 'POST', {})
    matcher_post_matcher_orders_cancel_orderid = matcherPostMatcherOrdersCancelOrderId = Entry('matcher/orders/cancel/{orderId}', 'matcher', 'POST', {})
    matcher_post_matcher_orders_serialize = matcherPostMatcherOrdersSerialize = Entry('matcher/orders/serialize', 'matcher', 'POST', {})
    matcher_delete_matcher_orderbook_baseid_quoteid = matcherDeleteMatcherOrderbookBaseIdQuoteId = Entry('matcher/orderbook/{baseId}/{quoteId}', 'matcher', 'DELETE', {})
    matcher_delete_matcher_settings_rates_assetid = matcherDeleteMatcherSettingsRatesAssetId = Entry('matcher/settings/rates/{assetId}', 'matcher', 'DELETE', {})
    matcher_put_matcher_settings_rates_assetid = matcherPutMatcherSettingsRatesAssetId = Entry('matcher/settings/rates/{assetId}', 'matcher', 'PUT', {})
    node_get_addresses = nodeGetAddresses = Entry('addresses', 'node', 'GET', {})
    node_get_addresses_balance_address = nodeGetAddressesBalanceAddress = Entry('addresses/balance/{address}', 'node', 'GET', {})
    node_get_addresses_balance_address_confirmations = nodeGetAddressesBalanceAddressConfirmations = Entry('addresses/balance/{address}/{confirmations}', 'node', 'GET', {})
    node_get_addresses_balance_details_address = nodeGetAddressesBalanceDetailsAddress = Entry('addresses/balance/details/{address}', 'node', 'GET', {})
    node_get_addresses_data_address = nodeGetAddressesDataAddress = Entry('addresses/data/{address}', 'node', 'GET', {})
    node_get_addresses_data_address_key = nodeGetAddressesDataAddressKey = Entry('addresses/data/{address}/{key}', 'node', 'GET', {})
    node_get_addresses_effectivebalance_address = nodeGetAddressesEffectiveBalanceAddress = Entry('addresses/effectiveBalance/{address}', 'node', 'GET', {})
    node_get_addresses_effectivebalance_address_confirmations = nodeGetAddressesEffectiveBalanceAddressConfirmations = Entry('addresses/effectiveBalance/{address}/{confirmations}', 'node', 'GET', {})
    node_get_addresses_publickey_publickey = nodeGetAddressesPublicKeyPublicKey = Entry('addresses/publicKey/{publicKey}', 'node', 'GET', {})
    node_get_addresses_scriptinfo_address = nodeGetAddressesScriptInfoAddress = Entry('addresses/scriptInfo/{address}', 'node', 'GET', {})
    node_get_addresses_scriptinfo_address_meta = nodeGetAddressesScriptInfoAddressMeta = Entry('addresses/scriptInfo/{address}/meta', 'node', 'GET', {})
    node_get_addresses_seed_address = nodeGetAddressesSeedAddress = Entry('addresses/seed/{address}', 'node', 'GET', {})
    node_get_addresses_seq_from_to = nodeGetAddressesSeqFromTo = Entry('addresses/seq/{from}/{to}', 'node', 'GET', {})
    node_get_addresses_validate_address = nodeGetAddressesValidateAddress = Entry('addresses/validate/{address}', 'node', 'GET', {})
    node_get_alias_by_address_address = nodeGetAliasByAddressAddress = Entry('alias/by-address/{address}', 'node', 'GET', {})
    node_get_alias_by_alias_alias = nodeGetAliasByAliasAlias = Entry('alias/by-alias/{alias}', 'node', 'GET', {})
    node_get_assets_assetid_distribution_height_limit = nodeGetAssetsAssetIdDistributionHeightLimit = Entry('assets/{assetId}/distribution/{height}/{limit}', 'node', 'GET', {})
    node_get_assets_balance_address = nodeGetAssetsBalanceAddress = Entry('assets/balance/{address}', 'node', 'GET', {})
    node_get_assets_balance_address_assetid = nodeGetAssetsBalanceAddressAssetId = Entry('assets/balance/{address}/{assetId}', 'node', 'GET', {})
    node_get_assets_details_assetid = nodeGetAssetsDetailsAssetId = Entry('assets/details/{assetId}', 'node', 'GET', {})
    node_get_assets_nft_address_limit_limit = nodeGetAssetsNftAddressLimitLimit = Entry('assets/nft/{address}/limit/{limit}', 'node', 'GET', {})
    node_get_blockchain_rewards = nodeGetBlockchainRewards = Entry('blockchain/rewards', 'node', 'GET', {})
    node_get_blockchain_rewards_height = nodeGetBlockchainRewardsHeight = Entry('blockchain/rewards/height', 'node', 'GET', {})
    node_get_blocks_address_address_from_to = nodeGetBlocksAddressAddressFromTo = Entry('blocks/address/{address}/{from}/{to}/', 'node', 'GET', {})
    node_get_blocks_at_height = nodeGetBlocksAtHeight = Entry('blocks/at/{height}', 'node', 'GET', {})
    node_get_blocks_delay_signature_blocknum = nodeGetBlocksDelaySignatureBlockNum = Entry('blocks/delay/{signature}/{blockNum}', 'node', 'GET', {})
    node_get_blocks_first = nodeGetBlocksFirst = Entry('blocks/first', 'node', 'GET', {})
    node_get_blocks_headers_last = nodeGetBlocksHeadersLast = Entry('blocks/headers/last', 'node', 'GET', {})
    node_get_blocks_headers_seq_from_to = nodeGetBlocksHeadersSeqFromTo = Entry('blocks/headers/seq/{from}/{to}', 'node', 'GET', {})
    node_get_blocks_height = nodeGetBlocksHeight = Entry('blocks/height', 'node', 'GET', {})
    node_get_blocks_height_signature = nodeGetBlocksHeightSignature = Entry('blocks/height/{signature}', 'node', 'GET', {})
    node_get_blocks_last = nodeGetBlocksLast = Entry('blocks/last', 'node', 'GET', {})
    node_get_blocks_seq_from_to = nodeGetBlocksSeqFromTo = Entry('blocks/seq/{from}/{to}', 'node', 'GET', {})
    node_get_blocks_signature_signature = nodeGetBlocksSignatureSignature = Entry('blocks/signature/{signature}', 'node', 'GET', {})
    node_get_consensus_algo = nodeGetConsensusAlgo = Entry('consensus/algo', 'node', 'GET', {})
    node_get_consensus_basetarget = nodeGetConsensusBasetarget = Entry('consensus/basetarget', 'node', 'GET', {})
    node_get_consensus_basetarget_blockid = nodeGetConsensusBasetargetBlockId = Entry('consensus/basetarget/{blockId}', 'node', 'GET', {})
    node_get_consensus_generatingbalance_address = nodeGetConsensusGeneratingbalanceAddress = Entry('consensus/{generatingbalance}/address', 'node', 'GET', {})
    node_get_consensus_generationsignature = nodeGetConsensusGenerationsignature = Entry('consensus/generationsignature', 'node', 'GET', {})
    node_get_consensus_generationsignature_blockid = nodeGetConsensusGenerationsignatureBlockId = Entry('consensus/generationsignature/{blockId}', 'node', 'GET', {})
    node_get_debug_balances_history_address = nodeGetDebugBalancesHistoryAddress = Entry('debug/balances/history/{address}', 'node', 'GET', {})
    node_get_debug_blocks_howmany = nodeGetDebugBlocksHowMany = Entry('debug/blocks/{howMany}', 'node', 'GET', {})
    node_get_debug_configinfo = nodeGetDebugConfigInfo = Entry('debug/configInfo', 'node', 'GET', {})
    node_get_debug_historyinfo = nodeGetDebugHistoryInfo = Entry('debug/historyInfo', 'node', 'GET', {})
    node_get_debug_info = nodeGetDebugInfo = Entry('debug/info', 'node', 'GET', {})
    node_get_debug_minerinfo = nodeGetDebugMinerInfo = Entry('debug/minerInfo', 'node', 'GET', {})
    node_get_debug_portfolios_address = nodeGetDebugPortfoliosAddress = Entry('debug/portfolios/{address}', 'node', 'GET', {})
    node_get_debug_state = nodeGetDebugState = Entry('debug/state', 'node', 'GET', {})
    node_get_debug_statechanges_address_address = nodeGetDebugStateChangesAddressAddress = Entry('debug/stateChanges/address/{address}', 'node', 'GET', {})
    node_get_debug_statechanges_info_id = nodeGetDebugStateChangesInfoId = Entry('debug/stateChanges/info/{id}', 'node', 'GET', {})
    node_get_debug_statewaves_height = nodeGetDebugStateWavesHeight = Entry('debug/stateWaves/{height}', 'node', 'GET', {})
    node_get_leasing_active_address = nodeGetLeasingActiveAddress = Entry('leasing/active/{address}', 'node', 'GET', {})
    node_get_node_state = nodeGetNodeState = Entry('node/state', 'node', 'GET', {})
    node_get_node_version = nodeGetNodeVersion = Entry('node/version', 'node', 'GET', {})
    node_get_peers_all = nodeGetPeersAll = Entry('peers/all', 'node', 'GET', {})
    node_get_peers_blacklisted = nodeGetPeersBlacklisted = Entry('peers/blacklisted', 'node', 'GET', {})
    node_get_peers_connected = nodeGetPeersConnected = Entry('peers/connected', 'node', 'GET', {})
    node_get_peers_suspended = nodeGetPeersSuspended = Entry('peers/suspended', 'node', 'GET', {})
    node_get_transactions_address_address_limit_limit = nodeGetTransactionsAddressAddressLimitLimit = Entry('transactions/address/{address}/limit/{limit}', 'node', 'GET', {})
    node_get_transactions_info_id = nodeGetTransactionsInfoId = Entry('transactions/info/{id}', 'node', 'GET', {})
    node_get_transactions_status = nodeGetTransactionsStatus = Entry('transactions/status', 'node', 'GET', {})
    node_get_transactions_unconfirmed = nodeGetTransactionsUnconfirmed = Entry('transactions/unconfirmed', 'node', 'GET', {})
    node_get_transactions_unconfirmed_info_id = nodeGetTransactionsUnconfirmedInfoId = Entry('transactions/unconfirmed/info/{id}', 'node', 'GET', {})
    node_get_transactions_unconfirmed_size = nodeGetTransactionsUnconfirmedSize = Entry('transactions/unconfirmed/size', 'node', 'GET', {})
    node_get_utils_seed = nodeGetUtilsSeed = Entry('utils/seed', 'node', 'GET', {})
    node_get_utils_seed_length = nodeGetUtilsSeedLength = Entry('utils/seed/{length}', 'node', 'GET', {})
    node_get_utils_time = nodeGetUtilsTime = Entry('utils/time', 'node', 'GET', {})
    node_get_wallet_seed = nodeGetWalletSeed = Entry('wallet/seed', 'node', 'GET', {})
    node_post_addresses = nodePostAddresses = Entry('addresses', 'node', 'POST', {})
    node_post_addresses_data_address = nodePostAddressesDataAddress = Entry('addresses/data/{address}', 'node', 'POST', {})
    node_post_addresses_sign_address = nodePostAddressesSignAddress = Entry('addresses/sign/{address}', 'node', 'POST', {})
    node_post_addresses_signtext_address = nodePostAddressesSignTextAddress = Entry('addresses/signText/{address}', 'node', 'POST', {})
    node_post_addresses_verify_address = nodePostAddressesVerifyAddress = Entry('addresses/verify/{address}', 'node', 'POST', {})
    node_post_addresses_verifytext_address = nodePostAddressesVerifyTextAddress = Entry('addresses/verifyText/{address}', 'node', 'POST', {})
    node_post_debug_blacklist = nodePostDebugBlacklist = Entry('debug/blacklist', 'node', 'POST', {})
    node_post_debug_print = nodePostDebugPrint = Entry('debug/print', 'node', 'POST', {})
    node_post_debug_rollback = nodePostDebugRollback = Entry('debug/rollback', 'node', 'POST', {})
    node_post_debug_validate = nodePostDebugValidate = Entry('debug/validate', 'node', 'POST', {})
    node_post_node_stop = nodePostNodeStop = Entry('node/stop', 'node', 'POST', {})
    node_post_peers_clearblacklist = nodePostPeersClearblacklist = Entry('peers/clearblacklist', 'node', 'POST', {})
    node_post_peers_connect = nodePostPeersConnect = Entry('peers/connect', 'node', 'POST', {})
    node_post_transactions_broadcast = nodePostTransactionsBroadcast = Entry('transactions/broadcast', 'node', 'POST', {})
    node_post_transactions_calculatefee = nodePostTransactionsCalculateFee = Entry('transactions/calculateFee', 'node', 'POST', {})
    node_post_tranasctions_sign = nodePostTranasctionsSign = Entry('tranasctions/sign', 'node', 'POST', {})
    node_post_transactions_sign_signeraddress = nodePostTransactionsSignSignerAddress = Entry('transactions/sign/{signerAddress}', 'node', 'POST', {})
    node_post_tranasctions_status = nodePostTranasctionsStatus = Entry('tranasctions/status', 'node', 'POST', {})
    node_post_utils_hash_fast = nodePostUtilsHashFast = Entry('utils/hash/fast', 'node', 'POST', {})
    node_post_utils_hash_secure = nodePostUtilsHashSecure = Entry('utils/hash/secure', 'node', 'POST', {})
    node_post_utils_script_compilecode = nodePostUtilsScriptCompileCode = Entry('utils/script/compileCode', 'node', 'POST', {})
    node_post_utils_script_compilewithimports = nodePostUtilsScriptCompileWithImports = Entry('utils/script/compileWithImports', 'node', 'POST', {})
    node_post_utils_script_decompile = nodePostUtilsScriptDecompile = Entry('utils/script/decompile', 'node', 'POST', {})
    node_post_utils_script_estimate = nodePostUtilsScriptEstimate = Entry('utils/script/estimate', 'node', 'POST', {})
    node_post_utils_sign_privatekey = nodePostUtilsSignPrivateKey = Entry('utils/sign/{privateKey}', 'node', 'POST', {})
    node_post_utils_transactionsserialize = nodePostUtilsTransactionsSerialize = Entry('utils/transactionsSerialize', 'node', 'POST', {})
    node_delete_addresses_address = nodeDeleteAddressesAddress = Entry('addresses/{address}', 'node', 'DELETE', {})
    node_delete_debug_rollback_to_signature = nodeDeleteDebugRollbackToSignature = Entry('debug/rollback-to/{signature}', 'node', 'DELETE', {})
    public_get_assets = publicGetAssets = Entry('assets', 'public', 'GET', {})
    public_get_pairs = publicGetPairs = Entry('pairs', 'public', 'GET', {})
    public_get_candles_baseid_quoteid = publicGetCandlesBaseIdQuoteId = Entry('candles/{baseId}/{quoteId}', 'public', 'GET', {})
    public_get_transactions_exchange = publicGetTransactionsExchange = Entry('transactions/exchange', 'public', 'GET', {})
    private_get_deposit_addresses_currency = privateGetDepositAddressesCurrency = Entry('deposit/addresses/{currency}', 'private', 'GET', {})
    private_get_deposit_addresses_currency_platform = privateGetDepositAddressesCurrencyPlatform = Entry('deposit/addresses/{currency}/{platform}', 'private', 'GET', {})
    private_get_platforms = privateGetPlatforms = Entry('platforms', 'private', 'GET', {})
    private_get_deposit_currencies = privateGetDepositCurrencies = Entry('deposit/currencies', 'private', 'GET', {})
    private_get_withdraw_currencies = privateGetWithdrawCurrencies = Entry('withdraw/currencies', 'private', 'GET', {})
    private_get_withdraw_addresses_currency_address = privateGetWithdrawAddressesCurrencyAddress = Entry('withdraw/addresses/{currency}/{address}', 'private', 'GET', {})
    private_post_oauth2_token = privatePostOauth2Token = Entry('oauth2/token', 'private', 'POST', {})
    forward_get_matcher_orders_address = forwardGetMatcherOrdersAddress = Entry('matcher/orders/{address}', 'forward', 'GET', {})
    forward_get_matcher_orders_address_orderid = forwardGetMatcherOrdersAddressOrderId = Entry('matcher/orders/{address}/{orderId}', 'forward', 'GET', {})
    forward_post_matcher_orders_wavesaddress_cancel = forwardPostMatcherOrdersWavesAddressCancel = Entry('matcher/orders/{wavesAddress}/cancel', 'forward', 'POST', {})
    market_get_tickers = marketGetTickers = Entry('tickers', 'market', 'GET', {})
